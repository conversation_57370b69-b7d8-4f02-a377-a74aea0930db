#!/usr/bin/env node

import { readFileSync, writeFileSync } from 'fs';
import { glob } from 'glob';

const replacements = {
  'bg-background': 'bg-white dark:bg-gray-900',
  'text-foreground': 'text-gray-900 dark:text-gray-100',
  'border-border': 'border-gray-200 dark:border-gray-700',
  'bg-card': 'bg-white dark:bg-gray-800',
  'text-card-foreground': 'text-gray-900 dark:text-gray-100',
  'bg-muted': 'bg-gray-100 dark:bg-gray-800',
  'text-muted-foreground': 'text-gray-500 dark:text-gray-400',
  'bg-accent': 'bg-gray-100 dark:bg-gray-800',
  'text-accent-foreground': 'text-gray-900 dark:text-gray-100',
  'bg-primary': 'bg-blue-500',
  'text-primary-foreground': 'text-white',
  'bg-secondary': 'bg-gray-100 dark:bg-gray-800',
  'text-secondary-foreground': 'text-gray-900 dark:text-gray-100',
  'bg-destructive': 'bg-red-500',
  'text-destructive-foreground': 'text-white',
  'border-input': 'border-gray-200 dark:border-gray-700',
  'ring-offset-background': 'ring-offset-white dark:ring-offset-gray-900',
  'ring-ring': 'ring-blue-500',
  'text-primary': 'text-blue-500',
  'hover:bg-primary/90': 'hover:bg-blue-600',
  'hover:bg-destructive/90': 'hover:bg-red-600',
  'hover:bg-accent': 'hover:bg-gray-200 dark:hover:bg-gray-700',
  'hover:text-accent-foreground': 'hover:text-gray-900 dark:hover:text-gray-100',
  'hover:bg-secondary/80': 'hover:bg-gray-200 dark:hover:bg-gray-700',
  'placeholder:text-muted-foreground': 'placeholder:text-gray-500 dark:placeholder:text-gray-400',
  'bg-popover': 'bg-white dark:bg-gray-800',
  'text-popover-foreground': 'text-gray-900 dark:text-gray-100',
  'prose-headings:text-foreground': 'prose-headings:text-gray-900 dark:prose-headings:text-gray-100',
  'prose-p:text-foreground': 'prose-p:text-gray-900 dark:prose-p:text-gray-100',
  'prose-a:text-primary': 'prose-a:text-blue-500',
  'hover:prose-a:text-primary/80': 'hover:prose-a:text-blue-600',
  'prose-strong:text-foreground': 'prose-strong:text-gray-900 dark:prose-strong:text-gray-100',
  'prose-em:text-foreground': 'prose-em:text-gray-900 dark:prose-em:text-gray-100',
  'prose-code:text-foreground': 'prose-code:text-gray-900 dark:prose-code:text-gray-100',
  'prose-code:bg-muted': 'prose-code:bg-gray-100 dark:prose-code:bg-gray-800',
  'prose-pre:bg-muted': 'prose-pre:bg-gray-100 dark:prose-pre:bg-gray-800',
  'prose-pre:border-border': 'prose-pre:border-gray-200 dark:prose-pre:border-gray-700',
  'prose-blockquote:border-l-primary': 'prose-blockquote:border-l-blue-500',
  'prose-blockquote:text-muted-foreground': 'prose-blockquote:text-gray-500 dark:prose-blockquote:text-gray-400',
  'prose-hr:border-border': 'prose-hr:border-gray-200 dark:prose-hr:border-gray-700',
  'prose-ul:text-foreground': 'prose-ul:text-gray-900 dark:prose-ul:text-gray-100',
  'prose-ol:text-foreground': 'prose-ol:text-gray-900 dark:prose-ol:text-gray-100',
  'prose-li:text-foreground': 'prose-li:text-gray-900 dark:prose-li:text-gray-100'
};

async function fixTailwindClasses() {
  const files = await glob('src/**/*.{ts,tsx,js,jsx}');
  
  for (const file of files) {
    let content = readFileSync(file, 'utf8');
    let modified = false;
    
    for (const [oldClass, newClass] of Object.entries(replacements)) {
      if (content.includes(oldClass)) {
        content = content.replaceAll(oldClass, newClass);
        modified = true;
      }
    }
    
    if (modified) {
      writeFileSync(file, content);
      console.log(`Fixed: ${file}`);
    }
  }
}

fixTailwindClasses().catch(console.error);
