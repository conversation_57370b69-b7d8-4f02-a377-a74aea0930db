{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 602459608915477983], [14039947826026167952, "build_script_build", false, 17649019696614455724], [14525517306681678134, "build_script_build", false, 6138702273475889114], [6416823254013318197, "build_script_build", false, 9733447608264895489], [8324462083842905811, "build_script_build", false, 9300140252279540283], [4972584477725338812, "build_script_build", false, 9897057874290654088], [5008152033733129214, "build_script_build", false, 10714153219781908101]], "local": [{"RerunIfChanged": {"output": "debug/build/app-f3d3ff89142cbc9d/output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}