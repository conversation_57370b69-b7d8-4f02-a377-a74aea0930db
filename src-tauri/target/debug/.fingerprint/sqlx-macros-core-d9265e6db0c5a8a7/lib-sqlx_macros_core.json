{"rustc": 12610991425282158916, "features": "[\"_rt-tokio\", \"_sqlite\", \"_tls-rustls-ring-webpki\", \"default\", \"derive\", \"json\", \"macros\", \"migrate\", \"sqlite\", \"sqlx-sqlite\", \"time\", \"tokio\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_sqlite\", \"_tls-native-tls\", \"_tls-rustls-aws-lc-rs\", \"_tls-rustls-ring-native-roots\", \"_tls-rustls-ring-webpki\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"derive\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlite-unbundled\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 12675354917325698667, "path": 17794125215319996476, "deps": [[530211389790465181, "hex", false, 8335660249270139797], [3060637413840920116, "proc_macro2", false, 12942862244320848046], [3150220818285335163, "url", false, 4501611970282967705], [3405707034081185165, "dotenvy", false, 666277374937785675], [3722963349756955755, "once_cell", false, 10848099642517938363], [4974441333307933176, "syn", false, 7377666127517925813], [9689903380558560274, "serde", false, 9215983834651162020], [9857275760291862238, "sha2", false, 9108065293426029987], [10776111606377762245, "sqlx_core", false, 3066266796540305046], [12170264697963848012, "either", false, 14719426382970995389], [12393800526703971956, "tokio", false, 9746568082639275678], [13077543566650298139, "heck", false, 3781367436225524150], [15367738274754116744, "serde_json", false, 5097336804711992693], [17038106176255014628, "sqlx_sqlite", false, 485923545501532159], [17990358020177143287, "quote", false, 3678157888405312570]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-macros-core-d9265e6db0c5a8a7/dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}