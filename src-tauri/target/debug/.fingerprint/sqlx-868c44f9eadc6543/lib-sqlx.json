{"rustc": 12610991425282158916, "features": "[\"_rt-tokio\", \"_sqlite\", \"any\", \"default\", \"derive\", \"json\", \"macros\", \"migrate\", \"runtime-tokio\", \"runtime-tokio-rustls\", \"sqlite\", \"sqlx-macros\", \"sqlx-sqlite\", \"time\", \"tls-rustls-ring\", \"tls-rustls-ring-webpki\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_sqlite\", \"_unstable-all-types\", \"all-databases\", \"any\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"default\", \"derive\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"regexp\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sqlite\", \"sqlite-preupdate-hook\", \"sqlite-unbundled\", \"sqlx-macros\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tls-native-tls\", \"tls-none\", \"tls-rustls\", \"tls-rustls-aws-lc-rs\", \"tls-rustls-ring\", \"tls-rustls-ring-native-roots\", \"tls-rustls-ring-webpki\", \"uuid\"]", "target": 3003836824758849296, "profile": 5347358027863023418, "path": 15348646691899365235, "deps": [[3276107248499827220, "sqlx_macros", false, 13940967591356148975], [10776111606377762245, "sqlx_core", false, 18308935959190992575], [17038106176255014628, "sqlx_sqlite", false, 9614113480102369936]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-868c44f9eadc6543/dep-lib-sqlx", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}