{"rustc": 12610991425282158916, "features": "[\"alloc\", \"ring\", \"std\"]", "declared_features": "[\"alloc\", \"aws-lc-rs\", \"aws-lc-rs-fips\", \"default\", \"ring\", \"std\"]", "target": 5054897795206437336, "profile": 5347358027863023418, "path": 3416369582431734379, "deps": [[2883436298747778685, "pki_types", false, 4568181026015682631], [5491919304041016563, "ring", false, 1322083191435538723], [8995469080876806959, "untrusted", false, 13921146186355599316]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustls-webpki-9657d02f699776bb/dep-lib-webpki", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}