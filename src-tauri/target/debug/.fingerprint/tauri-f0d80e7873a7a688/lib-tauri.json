{"rustc": 12610991425282158916, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 5347358027863023418, "path": 8845635918973955678, "deps": [[40386456601120721, "percent_encoding", false, 4199467447753775118], [1200537532907108615, "url<PERSON><PERSON>n", false, 9783388998079427117], [1386409696764982933, "objc2", false, 6018824927452420370], [2671782512663819132, "tauri_utils", false, 7005710568642286752], [3150220818285335163, "url", false, 4020185935880617956], [3331586631144870129, "getrandom", false, 15277724055666098677], [4143744114649553716, "raw_window_handle", false, 14883712900136532333], [4494683389616423722, "muda", false, 4969762442051254747], [4919829919303820331, "serialize_to_javascript", false, 9868932029426650016], [5986029879202738730, "log", false, 1073214228640974174], [6089812615193535349, "tauri_runtime", false, 1722205962066683196], [7309744164794866499, "plist", false, 1144403644029075435], [7573826311589115053, "tauri_macros", false, 4320225200513325558], [8589231650440095114, "embed_plist", false, 16050859780445546616], [9010263965687315507, "http", false, 16496804331466554205], [9689903380558560274, "serde", false, 9215983834651162020], [9859211262912517217, "objc2_foundation", false, 16546875538924491700], [10229185211513642314, "mime", false, 10669889744104709973], [10575598148575346675, "objc2_app_kit", false, 10666254899626758134], [10806645703491011684, "thiserror", false, 9944961540414836834], [11599800339996261026, "tauri_runtime_wry", false, 757298229608798684], [11989259058781683633, "dunce", false, 8998476152632426829], [12393800526703971956, "tokio", false, 5081389266634996015], [12565293087094287914, "window_vibrancy", false, 1541261119705921424], [12986574360607194341, "serde_repr", false, 7959260248623282062], [13077543566650298139, "heck", false, 3781367436225524150], [13625485746686963219, "anyhow", false, 6785053432629220900], [14039947826026167952, "build_script_build", false, 17649019696614455724], [15367738274754116744, "serde_json", false, 16665596492421878620], [16928111194414003569, "dirs", false, 13061245219314176062], [17155886227862585100, "glob", false, 17551662316496281311]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-f0d80e7873a7a688/dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}