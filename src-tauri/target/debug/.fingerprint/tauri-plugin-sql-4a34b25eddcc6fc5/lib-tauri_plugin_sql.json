{"rustc": 12610991425282158916, "features": "[\"sqlite\"]", "declared_features": "[\"mysql\", \"postgres\", \"sqlite\"]", "target": 2554770350637848829, "profile": 5347358027863023418, "path": 9668542363927496832, "deps": [[5008152033733129214, "build_script_build", false, 10714153219781908101], [5986029879202738730, "log", false, 1073214228640974174], [6493259146304816786, "indexmap", false, 10614316106077111411], [6841140121864026414, "sqlx", false, 16297324466083754395], [7620660491849607393, "futures_core", false, 10107320479870344271], [9689903380558560274, "serde", false, 9215983834651162020], [10806645703491011684, "thiserror", false, 9944961540414836834], [12393800526703971956, "tokio", false, 5081389266634996015], [12409575957772518135, "time", false, 5600661607622881225], [14039947826026167952, "tauri", false, 9313658313095221210], [15367738274754116744, "serde_json", false, 16665596492421878620]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-plugin-sql-4a34b25eddcc6fc5/dep-lib-tauri_plugin_sql", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}