{"rustc": 12610991425282158916, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 5347358027863023418, "path": 8739948878028934930, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 9783388998079427117], [3150220818285335163, "url", false, 4020185935880617956], [3191507132440681679, "serde_untagged", false, 673706747915244852], [4071963112282141418, "serde_with", false, 2051891065881340424], [4899080583175475170, "semver", false, 10142119383020910351], [5986029879202738730, "log", false, 1073214228640974174], [6606131838865521726, "ctor", false, 18207635291306989299], [7170110829644101142, "json_patch", false, 1031709678457123377], [8319709847752024821, "uuid", false, 4484539864848886862], [9010263965687315507, "http", false, 16496804331466554205], [9451456094439810778, "regex", false, 10900885324046962223], [9556762810601084293, "brotli", false, 11586902715152017763], [9689903380558560274, "serde", false, 9215983834651162020], [10806645703491011684, "thiserror", false, 9944961540414836834], [11989259058781683633, "dunce", false, 8998476152632426829], [13625485746686963219, "anyhow", false, 6785053432629220900], [15367738274754116744, "serde_json", false, 16665596492421878620], [15609422047640926750, "toml", false, 8263138237384064498], [15622660310229662834, "walkdir", false, 10025403731275707983], [15932120279885307830, "memchr", false, 7330887791526293607], [17146114186171651583, "infer", false, 7145033489449896220], [17155886227862585100, "glob", false, 17551662316496281311], [17186037756130803222, "phf", false, 4209212595418736239]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-utils-d668ee93892030de/dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}