{"rustc": 12610991425282158916, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 5347358027863023418, "path": 11721507329855939718, "deps": [[1386409696764982933, "objc2", false, 6018824927452420370], [2671782512663819132, "tauri_utils", false, 7005710568642286752], [3150220818285335163, "url", false, 4020185935880617956], [4143744114649553716, "raw_window_handle", false, 14883712900136532333], [5986029879202738730, "log", false, 1073214228640974174], [6089812615193535349, "tauri_runtime", false, 1722205962066683196], [8826339825490770380, "tao", false, 10767516195487380467], [9010263965687315507, "http", false, 16496804331466554205], [9141053277961803901, "wry", false, 10395652145404035693], [9859211262912517217, "objc2_foundation", false, 16546875538924491700], [10575598148575346675, "objc2_app_kit", false, 10666254899626758134], [11599800339996261026, "build_script_build", false, 13443598448710101566]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-wry-cf95516ffdeb333e/dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}