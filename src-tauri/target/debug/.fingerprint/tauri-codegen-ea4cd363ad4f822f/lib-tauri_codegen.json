{"rustc": 12610991425282158916, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 3033921117576893, "path": 8844259476180029965, "deps": [[2671782512663819132, "tauri_utils", false, 2727080880759437304], [3060637413840920116, "proc_macro2", false, 12942862244320848046], [3150220818285335163, "url", false, 4501611970282967705], [4899080583175475170, "semver", false, 15807172358233526613], [4974441333307933176, "syn", false, 7377666127517925813], [7170110829644101142, "json_patch", false, 9718997819913652700], [7309744164794866499, "plist", false, 2497989090453158373], [7392050791754369441, "ico", false, 5375671504180537787], [8319709847752024821, "uuid", false, 1917612549167401048], [9556762810601084293, "brotli", false, 11586902715152017763], [9689903380558560274, "serde", false, 9215983834651162020], [9857275760291862238, "sha2", false, 9108065293426029987], [10806645703491011684, "thiserror", false, 9944961540414836834], [12409575957772518135, "time", false, 18357536743544509248], [12687914511023397207, "png", false, 3633402842010461373], [13077212702700853852, "base64", false, 5903772373444819258], [15367738274754116744, "serde_json", false, 5097336804711992693], [15622660310229662834, "walkdir", false, 10025403731275707983], [17990358020177143287, "quote", false, 3678157888405312570]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-codegen-ea4cd363ad4f822f/dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}