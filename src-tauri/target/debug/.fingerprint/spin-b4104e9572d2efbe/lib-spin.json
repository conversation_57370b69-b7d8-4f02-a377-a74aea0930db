{"rustc": 12610991425282158916, "features": "[\"barrier\", \"default\", \"lazy\", \"lock_api\", \"lock_api_crate\", \"mutex\", \"once\", \"rwlock\", \"spin_mutex\"]", "declared_features": "[\"barrier\", \"default\", \"fair_mutex\", \"lazy\", \"lock_api\", \"lock_api_crate\", \"mutex\", \"once\", \"portable-atomic\", \"portable_atomic\", \"rwlock\", \"spin_mutex\", \"std\", \"ticket_mutex\", \"use_ticket_mutex\"]", "target": 4260413527236709406, "profile": 5347358027863023418, "path": 2382462726076207101, "deps": [[8081351675046095464, "lock_api_crate", false, 10234380495461520959]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spin-b4104e9572d2efbe/dep-lib-spin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}