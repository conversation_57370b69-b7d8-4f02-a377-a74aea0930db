{"rustc": 12610991425282158916, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 3033921117576893, "path": 8739948878028934930, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 238850999623571597], [3060637413840920116, "proc_macro2", false, 12942862244320848046], [3150220818285335163, "url", false, 4501611970282967705], [3191507132440681679, "serde_untagged", false, 673706747915244852], [4071963112282141418, "serde_with", false, 2051891065881340424], [4899080583175475170, "semver", false, 15807172358233526613], [5986029879202738730, "log", false, 17375917083657240212], [6606131838865521726, "ctor", false, 18207635291306989299], [6913375703034175521, "schemars", false, 6120690794932245276], [7170110829644101142, "json_patch", false, 9718997819913652700], [8319709847752024821, "uuid", false, 1917612549167401048], [9010263965687315507, "http", false, 16496804331466554205], [9451456094439810778, "regex", false, 10900885324046962223], [9556762810601084293, "brotli", false, 11586902715152017763], [9689903380558560274, "serde", false, 9215983834651162020], [10806645703491011684, "thiserror", false, 9944961540414836834], [11655476559277113544, "cargo_metadata", false, 4423311024056959804], [11989259058781683633, "dunce", false, 8998476152632426829], [13625485746686963219, "anyhow", false, 6785053432629220900], [14232843520438415263, "html5ever", false, 18044002695256125588], [14885200901422974105, "swift_rs", false, 3381331440748448177], [15088007382495681292, "kuchiki", false, 14510775971240812509], [15367738274754116744, "serde_json", false, 5097336804711992693], [15609422047640926750, "toml", false, 11276643991472066059], [15622660310229662834, "walkdir", false, 10025403731275707983], [15932120279885307830, "memchr", false, 7330887791526293607], [17146114186171651583, "infer", false, 5643207070458815612], [17155886227862585100, "glob", false, 17551662316496281311], [17186037756130803222, "phf", false, 5153428022384591436], [17990358020177143287, "quote", false, 3678157888405312570]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-utils-4a70c86dac4eee38/dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}