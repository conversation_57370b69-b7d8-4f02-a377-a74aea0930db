{"rustc": 12610991425282158916, "features": "[\"attributes\", \"default\", \"log\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 1006155289083248400, "path": 6929379725633328481, "deps": [[325572602735163265, "tracing_attributes", false, 3825007975490431758], [1906322745568073236, "pin_project_lite", false, 3203675042107764010], [3424551429995674438, "tracing_core", false, 9830441917592234547], [5986029879202738730, "log", false, 17375917083657240212]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-fbb7310a9fd18c9e/dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}