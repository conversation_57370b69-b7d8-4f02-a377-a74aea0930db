{"rustc": 12610991425282158916, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 5073168975548574389, "path": 16486040775176028002, "deps": [[2883436298747778685, "pki_types", false, 4568181026015682631], [3722963349756955755, "once_cell", false, 10848099642517938363], [5491919304041016563, "ring", false, 1322083191435538723], [6528079939221783635, "zeroize", false, 9012904954670626279], [16400140949089969347, "build_script_build", false, 28671666244309031], [17003143334332120809, "subtle", false, 17576118869036623747], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 18205194722653139844]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustls-2d5636ea2b480717/dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}