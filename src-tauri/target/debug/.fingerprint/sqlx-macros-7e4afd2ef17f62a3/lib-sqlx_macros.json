{"rustc": 12610991425282158916, "features": "[\"_rt-tokio\", \"_tls-rustls-ring-webpki\", \"default\", \"derive\", \"json\", \"macros\", \"migrate\", \"sqlite\", \"time\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls-aws-lc-rs\", \"_tls-rustls-ring-native-roots\", \"_tls-rustls-ring-webpki\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"derive\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlite-unbundled\", \"time\", \"uuid\"]", "target": 13494433325021527976, "profile": 8870715365344955839, "path": 16492191310906253134, "deps": [[3060637413840920116, "proc_macro2", false, 12942862244320848046], [4974441333307933176, "syn", false, 7377666127517925813], [10654871823602349891, "sqlx_macros_core", false, 5591029708171775047], [10776111606377762245, "sqlx_core", false, 3066266796540305046], [17990358020177143287, "quote", false, 3678157888405312570]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-macros-7e4afd2ef17f62a3/dep-lib-sqlx_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}