{"rustc": 12610991425282158916, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"_tls-rustls-ring-webpki\", \"any\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"serde\", \"serde_json\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"_tls-rustls-aws-lc-rs\", \"_tls-rustls-ring-native-roots\", \"_tls-rustls-ring-webpki\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-native-certs\", \"serde\", \"serde_json\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 17036515905936661633, "path": 9941686574158775754, "deps": [[5103565458935487, "futures_io", false, 1719730289134170713], [40386456601120721, "percent_encoding", false, 4199467447753775118], [788558663644978524, "crossbeam_queue", false, 14318754930872154847], [1099106214093768284, "hashbrown", false, 2936138240992010076], [1162433738665300155, "crc", false, 1437775075716433961], [1303438375223863970, "hashlink", false, 8018477771108229444], [3150220818285335163, "url", false, 4020185935880617956], [3646857438214563691, "futures_intrusive", false, 2266189693904121399], [3666196340704888985, "smallvec", false, 8537861757941714488], [3722963349756955755, "once_cell", false, 10848099642517938363], [5986029879202738730, "log", false, 1073214228640974174], [6493259146304816786, "indexmap", false, 10614316106077111411], [7620660491849607393, "futures_core", false, 10107320479870344271], [8156804143951879168, "webpki_roots", false, 17630132854985687941], [8606274917505247608, "tracing", false, 11323958996171095259], [9061476533697426406, "event_listener", false, 9643047740608448390], [9689903380558560274, "serde", false, 9215983834651162020], [9857275760291862238, "sha2", false, 3588241213018581095], [10629569228670356391, "futures_util", false, 10991647694392776727], [10806645703491011684, "thiserror", false, 9944961540414836834], [12170264697963848012, "either", false, 14719426382970995389], [12393800526703971956, "tokio", false, 5081389266634996015], [12409575957772518135, "time", false, 5600661607622881225], [13077212702700853852, "base64", false, 5903772373444819258], [15367738274754116744, "serde_json", false, 16665596492421878620], [15932120279885307830, "memchr", false, 7330887791526293607], [16066129441945555748, "bytes", false, 17676314659758761950], [16400140949089969347, "rustls", false, 10200786596987105841], [16973251432615581304, "tokio_stream", false, 12057275588370024514]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-core-77356f7ce387110e/dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}