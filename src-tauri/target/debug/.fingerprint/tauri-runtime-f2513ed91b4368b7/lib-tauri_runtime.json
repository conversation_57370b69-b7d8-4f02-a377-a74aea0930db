{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 5347358027863023418, "path": 8251096935083598071, "deps": [[2671782512663819132, "tauri_utils", false, 7005710568642286752], [3150220818285335163, "url", false, 4020185935880617956], [4143744114649553716, "raw_window_handle", false, 14883712900136532333], [6089812615193535349, "build_script_build", false, 12112242897310774125], [7606335748176206944, "dpi", false, 2656442751062470511], [9010263965687315507, "http", false, 16496804331466554205], [9689903380558560274, "serde", false, 9215983834651162020], [10806645703491011684, "thiserror", false, 9944961540414836834], [15367738274754116744, "serde_json", false, 16665596492421878620], [16727543399706004146, "cookie", false, 13786160572923286983]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-f2513ed91b4368b7/dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}