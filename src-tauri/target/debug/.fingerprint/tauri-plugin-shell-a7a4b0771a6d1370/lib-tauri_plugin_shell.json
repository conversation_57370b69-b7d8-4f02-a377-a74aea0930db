{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 5347358027863023418, "path": 12788790534539316168, "deps": [[4972584477725338812, "build_script_build", false, 9897057874290654088], [5986029879202738730, "log", false, 1073214228640974174], [9451456094439810778, "regex", false, 10900885324046962223], [9689903380558560274, "serde", false, 9215983834651162020], [10806645703491011684, "thiserror", false, 9944961540414836834], [11337703028400419576, "os_pipe", false, 14930550099922405184], [12393800526703971956, "tokio", false, 5081389266634996015], [14039947826026167952, "tauri", false, 9313658313095221210], [14564311161534545801, "encoding_rs", false, 8938628415404998194], [15367738274754116744, "serde_json", false, 16665596492421878620], [15722096100444777195, "shared_child", false, 1477737892970862617], [16192041687293812804, "open", false, 9946025131982873283]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-plugin-shell-a7a4b0771a6d1370/dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}