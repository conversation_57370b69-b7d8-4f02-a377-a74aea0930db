{"rustc": 12610991425282158916, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 3033921117576893, "path": 12659736334191314063, "deps": [[2671782512663819132, "tauri_utils", false, 2727080880759437304], [3060637413840920116, "proc_macro2", false, 12942862244320848046], [4974441333307933176, "syn", false, 7377666127517925813], [13077543566650298139, "heck", false, 3781367436225524150], [14455244907590647360, "tauri_codegen", false, 6923873949716745160], [17990358020177143287, "quote", false, 3678157888405312570]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-macros-89e1f6336ee4d644/dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}