["/Users/<USER>/Downloads/VSvode2/src-tauri/target/debug/build/tauri-e3c16c4a65792f7a/out/permissions/app/autogenerated/commands/app_hide.toml", "/Users/<USER>/Downloads/VSvode2/src-tauri/target/debug/build/tauri-e3c16c4a65792f7a/out/permissions/app/autogenerated/commands/app_show.toml", "/Users/<USER>/Downloads/VSvode2/src-tauri/target/debug/build/tauri-e3c16c4a65792f7a/out/permissions/app/autogenerated/commands/default_window_icon.toml", "/Users/<USER>/Downloads/VSvode2/src-tauri/target/debug/build/tauri-e3c16c4a65792f7a/out/permissions/app/autogenerated/commands/fetch_data_store_identifiers.toml", "/Users/<USER>/Downloads/VSvode2/src-tauri/target/debug/build/tauri-e3c16c4a65792f7a/out/permissions/app/autogenerated/commands/identifier.toml", "/Users/<USER>/Downloads/VSvode2/src-tauri/target/debug/build/tauri-e3c16c4a65792f7a/out/permissions/app/autogenerated/commands/name.toml", "/Users/<USER>/Downloads/VSvode2/src-tauri/target/debug/build/tauri-e3c16c4a65792f7a/out/permissions/app/autogenerated/commands/remove_data_store.toml", "/Users/<USER>/Downloads/VSvode2/src-tauri/target/debug/build/tauri-e3c16c4a65792f7a/out/permissions/app/autogenerated/commands/set_app_theme.toml", "/Users/<USER>/Downloads/VSvode2/src-tauri/target/debug/build/tauri-e3c16c4a65792f7a/out/permissions/app/autogenerated/commands/set_dock_visibility.toml", "/Users/<USER>/Downloads/VSvode2/src-tauri/target/debug/build/tauri-e3c16c4a65792f7a/out/permissions/app/autogenerated/commands/tauri_version.toml", "/Users/<USER>/Downloads/VSvode2/src-tauri/target/debug/build/tauri-e3c16c4a65792f7a/out/permissions/app/autogenerated/commands/version.toml", "/Users/<USER>/Downloads/VSvode2/src-tauri/target/debug/build/tauri-e3c16c4a65792f7a/out/permissions/app/autogenerated/default.toml"]