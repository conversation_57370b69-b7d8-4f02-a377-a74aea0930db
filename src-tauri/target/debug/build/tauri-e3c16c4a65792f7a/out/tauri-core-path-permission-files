["/Users/<USER>/Downloads/VSvode2/src-tauri/target/debug/build/tauri-e3c16c4a65792f7a/out/permissions/path/autogenerated/commands/basename.toml", "/Users/<USER>/Downloads/VSvode2/src-tauri/target/debug/build/tauri-e3c16c4a65792f7a/out/permissions/path/autogenerated/commands/dirname.toml", "/Users/<USER>/Downloads/VSvode2/src-tauri/target/debug/build/tauri-e3c16c4a65792f7a/out/permissions/path/autogenerated/commands/extname.toml", "/Users/<USER>/Downloads/VSvode2/src-tauri/target/debug/build/tauri-e3c16c4a65792f7a/out/permissions/path/autogenerated/commands/is_absolute.toml", "/Users/<USER>/Downloads/VSvode2/src-tauri/target/debug/build/tauri-e3c16c4a65792f7a/out/permissions/path/autogenerated/commands/join.toml", "/Users/<USER>/Downloads/VSvode2/src-tauri/target/debug/build/tauri-e3c16c4a65792f7a/out/permissions/path/autogenerated/commands/normalize.toml", "/Users/<USER>/Downloads/VSvode2/src-tauri/target/debug/build/tauri-e3c16c4a65792f7a/out/permissions/path/autogenerated/commands/resolve.toml", "/Users/<USER>/Downloads/VSvode2/src-tauri/target/debug/build/tauri-e3c16c4a65792f7a/out/permissions/path/autogenerated/commands/resolve_directory.toml", "/Users/<USER>/Downloads/VSvode2/src-tauri/target/debug/build/tauri-e3c16c4a65792f7a/out/permissions/path/autogenerated/default.toml"]