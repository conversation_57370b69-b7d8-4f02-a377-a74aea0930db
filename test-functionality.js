// Simple test script to verify core functionality
console.log('Testing Synthesis functionality...');

// Test 1: Check if stores are properly initialized
try {
  const { useNotesStore } = require('./src/stores/notesStore.ts');
  const { useUIStore } = require('./src/stores/uiStore.ts');
  const { useSettingsStore } = require('./src/stores/settingsStore.ts');
  console.log('✅ Stores imported successfully');
} catch (error) {
  console.log('❌ Store import failed:', error.message);
}

// Test 2: Check if services are properly initialized
try {
  const { aiService } = require('./src/services/aiService.ts');
  const { fileService } = require('./src/services/fileService.ts');
  const { notesService } = require('./src/services/notesService.ts');
  console.log('✅ Services imported successfully');
} catch (error) {
  console.log('❌ Service import failed:', error.message);
}

// Test 3: Check if components are properly exported
try {
  const { MarkdownEditor } = require('./src/components/editor/MarkdownEditor.tsx');
  const { MarkdownPreview } = require('./src/components/editor/MarkdownPreview.tsx');
  const { AICommandPalette } = require('./src/components/editor/AICommandPalette.tsx');
  console.log('✅ Editor components imported successfully');
} catch (error) {
  console.log('❌ Component import failed:', error.message);
}

console.log('Functionality test completed!');
