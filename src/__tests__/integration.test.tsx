import { describe, it, expect, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom';
import { useNotesStore } from '../stores/notesStore';
import { useUIStore } from '../stores/uiStore';
import { useSettingsStore } from '../stores/settingsStore';

// Mock Tauri APIs
const mockTauri = {
  invoke: vi.fn(),
  listen: vi.fn(),
  emit: vi.fn(),
  fs: {
    readTextFile: vi.fn(),
    writeTextFile: vi.fn(),
    exists: vi.fn(),
    mkdir: vi.fn(),
    readDir: vi.fn(),
  },
  dialog: {
    open: vi.fn(),
    save: vi.fn(),
  },
  path: {
    appDataDir: vi.fn().mockResolvedValue('/mock/app/data'),
    join: vi.fn().mockImplementation((...paths) => paths.join('/')),
  },
};

Object.defineProperty(window, '__TAURI__', {
  value: mockTauri,
  writable: true,
});

describe('Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset stores
    useUIStore.getState().setSelectedNote(null);
  });

  describe('Notes Store', () => {
    it('should create a new note', async () => {
      const store = useNotesStore.getState();
      
      const noteParams = {
        title: 'Test Note',
        content: 'This is a test note',
        type: 'raw' as const,
        sourceType: 'other' as const,
        tags: ['test']
      };

      // Mock file operations
      mockTauri.fs.writeTextFile.mockResolvedValue(undefined);
      mockTauri.fs.exists.mockResolvedValue(false);
      mockTauri.fs.mkdir.mockResolvedValue(undefined);

      const noteId = await store.createNote(noteParams);
      
      expect(noteId).toBeDefined();
      expect(store.rawNotes).toHaveLength(1);
      expect(store.rawNotes[0].title).toBe('Test Note');
      expect(store.rawNotes[0].content).toBe('This is a test note');
    });

    it('should update an existing note', async () => {
      const store = useNotesStore.getState();
      
      // Create a note first
      const noteParams = {
        title: 'Original Title',
        content: 'Original content',
        type: 'raw' as const,
        sourceType: 'other' as const,
        tags: []
      };

      mockTauri.fs.writeTextFile.mockResolvedValue(undefined);
      mockTauri.fs.exists.mockResolvedValue(false);
      mockTauri.fs.mkdir.mockResolvedValue(undefined);

      const noteId = await store.createNote(noteParams);
      
      // Update the note
      await store.updateNote({
        id: noteId,
        title: 'Updated Title',
        content: 'Updated content'
      });

      const updatedNote = store.getNoteById(noteId);
      expect(updatedNote?.title).toBe('Updated Title');
      expect(updatedNote?.content).toBe('Updated content');
    });

    it('should delete a note', async () => {
      const store = useNotesStore.getState();
      
      const noteParams = {
        title: 'Note to Delete',
        content: 'This note will be deleted',
        type: 'raw' as const,
        sourceType: 'other' as const,
        tags: []
      };

      mockTauri.fs.writeTextFile.mockResolvedValue(undefined);
      mockTauri.fs.exists.mockResolvedValue(false);
      mockTauri.fs.mkdir.mockResolvedValue(undefined);

      const noteId = await store.createNote(noteParams);
      expect(store.rawNotes).toHaveLength(1);

      // Mock file deletion
      mockTauri.invoke.mockResolvedValue(undefined);

      await store.deleteNote(noteId);
      expect(store.rawNotes).toHaveLength(0);
    });

    it('should search notes', async () => {
      const store = useNotesStore.getState();
      
      // Create multiple notes
      const notes = [
        { title: 'JavaScript Basics', content: 'Learning JavaScript fundamentals', type: 'raw' as const, sourceType: 'other' as const, tags: ['javascript'] },
        { title: 'React Components', content: 'Understanding React component patterns', type: 'raw' as const, sourceType: 'other' as const, tags: ['react'] },
        { title: 'TypeScript Guide', content: 'TypeScript best practices', type: 'raw' as const, sourceType: 'other' as const, tags: ['typescript'] }
      ];

      mockTauri.fs.writeTextFile.mockResolvedValue(undefined);
      mockTauri.fs.exists.mockResolvedValue(false);
      mockTauri.fs.mkdir.mockResolvedValue(undefined);

      for (const note of notes) {
        await store.createNote(note);
      }

      // Search for JavaScript
      const jsResults = store.searchNotes('JavaScript');
      expect(jsResults).toHaveLength(1);
      expect(jsResults[0].title).toBe('JavaScript Basics');

      // Search for React
      const reactResults = store.searchNotes('React');
      expect(reactResults).toHaveLength(1);
      expect(reactResults[0].title).toBe('React Components');

      // Search for a term that appears in content
      const practicesResults = store.searchNotes('practices');
      expect(practicesResults).toHaveLength(1);
      expect(practicesResults[0].title).toBe('TypeScript Guide');
    });
  });

  describe('UI Store', () => {
    it('should manage sidebar state', () => {
      const store = useUIStore.getState();
      
      expect(store.sidebarCollapsed).toBe(false);
      
      store.toggleSidebar();
      expect(store.sidebarCollapsed).toBe(true);
      
      store.toggleSidebar();
      expect(store.sidebarCollapsed).toBe(false);
    });

    it('should manage current view', () => {
      const store = useUIStore.getState();
      
      expect(store.currentView).toBe('split');
      
      store.setCurrentView('editor');
      expect(store.currentView).toBe('editor');
      
      store.setCurrentView('preview');
      expect(store.currentView).toBe('preview');
    });

    it('should manage selected note', () => {
      const store = useUIStore.getState();
      
      expect(store.selectedNoteId).toBeNull();
      
      store.setSelectedNote('test-note-id');
      expect(store.selectedNoteId).toBe('test-note-id');
      
      store.setSelectedNote(null);
      expect(store.selectedNoteId).toBeNull();
    });
  });

  describe('Settings Store', () => {
    it('should update AI provider settings', () => {
      const store = useSettingsStore.getState();
      
      expect(store.aiProvider).toBe('openai');
      
      store.updateSettings({
        aiProvider: 'anthropic',
        apiKey: 'test-key'
      });
      
      expect(store.aiProvider).toBe('anthropic');
      expect(store.apiKey).toBe('test-key');
    });

    it('should update theme settings', () => {
      const store = useSettingsStore.getState();
      
      expect(store.theme).toBe('system');
      
      store.updateSettings({ theme: 'dark' });
      expect(store.theme).toBe('dark');
      
      store.updateSettings({ theme: 'light' });
      expect(store.theme).toBe('light');
    });
  });
});
