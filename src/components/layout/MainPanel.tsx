import { Eye, Edit, Split, Save, MoreHorizontal } from 'lucide-react';
import { Button } from '../ui/Button';
import { Separator } from '../ui/Separator';
import { MarkdownEditor } from '../editor/MarkdownEditor';
import { MarkdownPreview } from '../editor/MarkdownPreview';
import { useUIStore } from '../../stores/uiStore';
import { useNotesStore } from '../../stores/notesStore';
import { cn } from '../../lib/utils';

interface MainPanelProps {
  className?: string;
}

export function MainPanel({ className }: MainPanelProps) {
  const {
    currentView,
    setCurrentView,
    selectedNoteId,
    editorState,
    setEditorContent,
    markEditorSaved
  } = useUIStore();
  
  const { getNoteById, updateNote } = useNotesStore();
  
  const selectedNote = selectedNoteId ? getNoteById(selectedNoteId) : null;
  
  const handleSave = async () => {
    if (!selectedNote || !editorState.isDirty) return;
    
    try {
      await updateNote({
        id: selectedNote.id,
        content: editorState.content
      });
      markEditorSaved();
    } catch (error) {
      console.error('Failed to save note:', error);
    }
  };

  const handleContentChange = (content: string) => {
    setEditorContent(content);
  };

  if (!selectedNote) {
    return (
      <div className={cn("flex-1 flex items-center justify-center bg-white dark:bg-gray-900", className)}>
        <div className="text-center text-gray-500 dark:text-gray-400">
          <Edit className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-medium mb-2">No note selected</h3>
          <p>Select a note from the sidebar to start editing</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex-1 flex flex-col bg-white dark:bg-gray-900", className)}>
      {/* Header */}
      <div className="h-14 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between px-4">
        <div className="flex items-center space-x-2">
          <h2 className="font-medium truncate">{selectedNote.title}</h2>
          {editorState.isDirty && (
            <span className="text-xs text-gray-500 dark:text-gray-400">• Unsaved</span>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          {/* View toggle */}
          <div className="flex items-center border border-gray-200 dark:border-gray-700 rounded-md">
            <Button
              variant={currentView === 'editor' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setCurrentView('editor')}
              className="rounded-r-none border-r"
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant={currentView === 'split' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setCurrentView('split')}
              className="rounded-none border-r"
            >
              <Split className="h-4 w-4" />
            </Button>
            <Button
              variant={currentView === 'preview' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setCurrentView('preview')}
              className="rounded-l-none"
            >
              <Eye className="h-4 w-4" />
            </Button>
          </div>
          
          <Separator orientation="vertical" className="h-6" />
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleSave}
            disabled={!editorState.isDirty}
          >
            <Save className="h-4 w-4 mr-2" />
            Save
          </Button>
          
          <Button variant="ghost" size="icon">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Content area */}
      <div className="flex-1 flex">
        {/* Editor */}
        {(currentView === 'editor' || currentView === 'split') && (
          <div className={cn(
            "flex-1 flex flex-col",
            currentView === 'split' && "border-r border-gray-200 dark:border-gray-700"
          )}>
            <div className="flex-1 p-4">
              <MarkdownEditor
                value={editorState.content || selectedNote.content}
                onChange={handleContentChange}
                placeholder="Start writing..."
                autoFocus={currentView === 'editor'}
              />
            </div>
          </div>
        )}

        {/* Preview */}
        {(currentView === 'preview' || currentView === 'split') && (
          <div className="flex-1 flex flex-col">
            <div className="flex-1 p-4">
              <MarkdownPreview
                content={editorState.content || selectedNote.content}
              />
            </div>
          </div>
        )}
      </div>
      
      {/* Status bar */}
      <div className="h-8 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between px-4 text-xs text-gray-500 dark:text-gray-400">
        <div className="flex items-center space-x-4">
          <span>
            {'sourceType' in selectedNote ? 'Raw Note' : 'Summary'}
          </span>
          {selectedNote.tags.length > 0 && (
            <span>
              Tags: {selectedNote.tags.join(', ')}
            </span>
          )}
        </div>
        <div className="flex items-center space-x-4">
          {editorState.lastSaved && (
            <span>
              Last saved: {editorState.lastSaved.toLocaleTimeString()}
            </span>
          )}
          <span>
            {(editorState.content || selectedNote.content).length} characters
          </span>
        </div>
      </div>
    </div>
  );
}
