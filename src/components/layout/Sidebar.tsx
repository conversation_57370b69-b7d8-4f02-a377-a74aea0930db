import { useState } from 'react';
import { Search, Plus, Settings, FileText, BookOpen, Download } from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Separator } from '../ui/Separator';
import { CreateNoteDialog } from '../dialogs/CreateNoteDialog';
import { SettingsDialog } from '../dialogs/SettingsDialog';
import { ImportExportDialog } from '../dialogs/ImportExportDialog';
import { useUIStore } from '../../stores/uiStore';
import { useNotesStore } from '../../stores/notesStore';
import { cn } from '../../lib/utils';

interface SidebarProps {
  className?: string;
}

export function Sidebar({ className }: SidebarProps) {
  const {
    sidebarCollapsed,
    searchQuery,
    setSearchQuery,
    setSelectedNote,
    selectedNoteId
  } = useUIStore();

  const { rawNotes, summarizedNotes, createNote, searchNotes } = useNotesStore();
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showSettingsDialog, setShowSettingsDialog] = useState(false);
  const [showImportExportDialog, setShowImportExportDialog] = useState(false);

  const searchResults = searchQuery ? searchNotes(searchQuery) : [...rawNotes, ...summarizedNotes];

  const handleCreateNote = async (params: any) => {
    const noteId = await createNote(params);
    setSelectedNote(noteId);
  };

  if (sidebarCollapsed) {
    return (
      <div className={cn("w-12 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col items-center py-4 space-y-2", className)}>
        <Button variant="ghost" size="icon" onClick={() => useUIStore.getState().toggleSidebar()}>
          <FileText className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="icon" onClick={() => setShowCreateDialog(true)}>
          <Plus className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="icon" onClick={() => setShowSettingsDialog(true)}>
          <Settings className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  return (
    <div className={cn("w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col", className)}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-lg font-semibold">Synthesis</h1>
          <Button variant="ghost" size="icon" onClick={() => setShowSettingsDialog(true)}>
            <Settings className="h-4 w-4" />
          </Button>
        </div>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500 dark:text-gray-400" />
          <Input
            placeholder="Search notes..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Create buttons */}
      <div className="p-4 space-y-2">
        <Button
          variant="outline"
          className="w-full justify-start"
          onClick={() => setShowCreateDialog(true)}
        >
          <Plus className="h-4 w-4 mr-2" />
          New Note
        </Button>
        <Button
          variant="outline"
          className="w-full justify-start"
          onClick={() => setShowImportExportDialog(true)}
        >
          <Download className="h-4 w-4 mr-2" />
          Import/Export
        </Button>
      </div>

      <Separator />

      {/* Notes list */}
      <div className="flex-1 overflow-y-auto">
        {searchResults.length === 0 ? (
          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
            {searchQuery ? 'No notes found' : 'No notes yet'}
          </div>
        ) : (
          <div className="p-2">
            {searchResults.map((note) => {
              const isRawNote = 'sourceType' in note;
              const isSelected = note.id === selectedNoteId;
              
              return (
                <div
                  key={note.id}
                  className={cn(
                    "p-3 rounded-md cursor-pointer hover:bg-gray-100 dark:bg-gray-800 transition-colors",
                    isSelected && "bg-gray-100 dark:bg-gray-800"
                  )}
                  onClick={() => setSelectedNote(note.id)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-sm truncate">{note.title}</h3>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 line-clamp-2">
                        {note.content.substring(0, 100)}...
                      </p>
                      <div className="flex items-center mt-2 space-x-2">
                        {isRawNote ? (
                          <FileText className="h-3 w-3 text-blue-500" />
                        ) : (
                          <BookOpen className="h-3 w-3 text-green-500" />
                        )}
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {new Date(note.updatedAt).toLocaleDateString()}
                        </span>
                      </div>
                      {note.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {note.tags.slice(0, 3).map((tag) => (
                            <span
                              key={tag}
                              className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded"
                            >
                              {tag}
                            </span>
                          ))}
                          {note.tags.length > 3 && (
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              +{note.tags.length - 3}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      <CreateNoteDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onCreateNote={handleCreateNote}
      />

      <SettingsDialog
        open={showSettingsDialog}
        onOpenChange={setShowSettingsDialog}
      />

      <ImportExportDialog
        open={showImportExportDialog}
        onOpenChange={setShowImportExportDialog}
      />
    </div>
  );
}
