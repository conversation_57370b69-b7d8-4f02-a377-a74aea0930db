import React, { useCallback, useEffect, useRef, useState } from 'react';
import { AICommandPalette } from './AICommandPalette';
import { useUIStore } from '../../stores/uiStore';
import { cn } from '../../lib/utils';

interface MarkdownEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  autoFocus?: boolean;
}

export function MarkdownEditor({
  value,
  onChange,
  placeholder = "Start writing...",
  className,
  autoFocus = false
}: MarkdownEditorProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const { setEditorSelection } = useUIStore();
  const [showAICommands, setShowAICommands] = useState(false);
  const [selectedText, setSelectedText] = useState('');

  // Auto-resize textarea
  const adjustHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${textarea.scrollHeight}px`;
    }
  }, []);

  useEffect(() => {
    adjustHeight();
  }, [value, adjustHeight]);

  useEffect(() => {
    if (autoFocus && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [autoFocus]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
  };

  const handleSelectionChange = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      setEditorSelection({
        start: textarea.selectionStart,
        end: textarea.selectionEnd
      });
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    // Handle Tab key for indentation
    if (e.key === 'Tab') {
      e.preventDefault();
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newValue = value.substring(0, start) + '  ' + value.substring(end);
      onChange(newValue);
      
      // Set cursor position after the inserted spaces
      setTimeout(() => {
        textarea.selectionStart = textarea.selectionEnd = start + 2;
      }, 0);
    }

    // Handle Enter key for list continuation
    if (e.key === 'Enter') {
      const start = textarea.selectionStart;
      const lines = value.substring(0, start).split('\n');
      const currentLine = lines[lines.length - 1];
      
      // Check for list patterns
      const listMatch = currentLine.match(/^(\s*)([-*+]|\d+\.)\s/);
      if (listMatch) {
        e.preventDefault();
        const indent = listMatch[1];
        const marker = listMatch[2];
        
        // If the current line only has the marker, remove it
        if (currentLine.trim() === marker) {
          const newValue = value.substring(0, start - currentLine.length) + 
                          indent + value.substring(start);
          onChange(newValue);
          setTimeout(() => {
            textarea.selectionStart = textarea.selectionEnd = start - currentLine.length + indent.length;
          }, 0);
        } else {
          // Continue the list
          const nextMarker = marker.match(/\d+/) ? 
            `${parseInt(marker) + 1}.` : marker;
          const newValue = value.substring(0, start) + 
                          `\n${indent}${nextMarker} ` + 
                          value.substring(textarea.selectionEnd);
          onChange(newValue);
          setTimeout(() => {
            textarea.selectionStart = textarea.selectionEnd = start + indent.length + nextMarker.length + 2;
          }, 0);
        }
      }
    }

    // Handle Cmd/Ctrl+K for AI commands
    if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
      e.preventDefault();

      // Get selected text
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const selected = value.substring(start, end);

      if (selected.trim()) {
        setSelectedText(selected);
        setShowAICommands(true);
      } else {
        // If no text selected, show a message or select current line
        const lines = value.split('\n');
        const currentPos = start;
        let lineStart = 0;
        let lineEnd = 0;
        let currentLine = '';

        for (let i = 0; i < lines.length; i++) {
          lineEnd = lineStart + lines[i].length;
          if (currentPos >= lineStart && currentPos <= lineEnd) {
            currentLine = lines[i];
            break;
          }
          lineStart = lineEnd + 1; // +1 for newline character
        }

        if (currentLine.trim()) {
          setSelectedText(currentLine);
          setShowAICommands(true);
        }
      }
    }
  };

  const handleApplyEdit = (newText: string) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;

    // Replace selected text with AI-generated text
    const newValue = value.substring(0, start) + newText + value.substring(end);
    onChange(newValue);

    // Set cursor position after the new text
    setTimeout(() => {
      textarea.selectionStart = textarea.selectionEnd = start + newText.length;
      textarea.focus();
    }, 0);
  };

  return (
    <div className={cn("relative flex-1", className)}>
      <textarea
        ref={textareaRef}
        value={value}
        onChange={handleChange}
        onSelect={handleSelectionChange}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className={cn(
          "w-full min-h-full resize-none border-none outline-none bg-transparent",
          "font-mono text-sm leading-relaxed",
          "placeholder:text-gray-500 dark:text-gray-400",
          "focus:ring-0 focus:outline-none",
          "overflow-hidden" // Hide scrollbar since we auto-resize
        )}
        style={{
          minHeight: '200px',
          maxHeight: 'none'
        }}
      />

      <AICommandPalette
        open={showAICommands}
        onOpenChange={setShowAICommands}
        selectedText={selectedText}
        onApplyEdit={handleApplyEdit}
      />
    </div>
  );
}
