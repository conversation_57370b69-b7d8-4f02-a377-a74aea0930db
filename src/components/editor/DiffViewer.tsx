import { Check, <PERSON>, <PERSON>ota<PERSON><PERSON>cw } from 'lucide-react';
import { Button } from '../ui/Button';
import { cn } from '../../lib/utils';

interface DiffViewerProps {
  originalText: string;
  suggestedText: string;
  onAccept: () => void;
  onReject: () => void;
  onRevert?: () => void;
  className?: string;
}

interface DiffLine {
  type: 'unchanged' | 'removed' | 'added';
  content: string;
  lineNumber?: number;
}

export function DiffViewer({
  originalText,
  suggestedText,
  onAccept,
  onReject,
  onRevert,
  className
}: DiffViewerProps) {
  // Simple diff algorithm - split by lines and compare
  const generateDiff = (original: string, suggested: string): DiffLine[] => {
    const originalLines = original.split('\n');
    const suggestedLines = suggested.split('\n');
    const diff: DiffLine[] = [];

    // Simple line-by-line comparison
    const maxLines = Math.max(originalLines.length, suggestedLines.length);
    
    for (let i = 0; i < maxLines; i++) {
      const originalLine = originalLines[i];
      const suggestedLine = suggestedLines[i];
      
      if (originalLine === suggestedLine) {
        // Unchanged line
        if (originalLine !== undefined) {
          diff.push({
            type: 'unchanged',
            content: originalLine,
            lineNumber: i + 1
          });
        }
      } else {
        // Line changed
        if (originalLine !== undefined) {
          diff.push({
            type: 'removed',
            content: originalLine,
            lineNumber: i + 1
          });
        }
        if (suggestedLine !== undefined) {
          diff.push({
            type: 'added',
            content: suggestedLine,
            lineNumber: i + 1
          });
        }
      }
    }

    return diff;
  };

  const diffLines = generateDiff(originalText, suggestedText);

  // If texts are identical, show a simple message
  if (originalText === suggestedText) {
    return (
      <div className={cn("border border-gray-200 dark:border-gray-700 rounded-lg p-4", className)}>
        <div className="text-center text-gray-500 dark:text-gray-400">
          No changes suggested
        </div>
        <div className="flex justify-center mt-4">
          <Button variant="outline" onClick={onReject}>
            Close
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden", className)}>
      {/* Header */}
      <div className="bg-gray-50 dark:bg-gray-800 px-4 py-2 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="font-medium text-sm">AI Suggestion</h3>
          <div className="flex items-center space-x-2">
            {onRevert && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onRevert}
                className="h-8 px-2"
              >
                <RotateCcw className="h-3 w-3 mr-1" />
                Revert
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={onReject}
              className="h-8 px-2 text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20"
            >
              <X className="h-3 w-3 mr-1" />
              Reject
            </Button>
            <Button
              size="sm"
              onClick={onAccept}
              className="h-8 px-2"
            >
              <Check className="h-3 w-3 mr-1" />
              Accept
            </Button>
          </div>
        </div>
      </div>

      {/* Diff Content */}
      <div className="max-h-96 overflow-y-auto">
        <div className="font-mono text-sm">
          {diffLines.map((line, index) => (
            <div
              key={index}
              className={cn(
                "flex",
                line.type === 'removed' && "bg-red-50 dark:bg-red-900/20",
                line.type === 'added' && "bg-green-50 dark:bg-green-900/20"
              )}
            >
              {/* Line indicator */}
              <div
                className={cn(
                  "w-8 flex-shrink-0 text-center py-1 text-xs",
                  "border-r border-gray-200 dark:border-gray-700",
                  line.type === 'removed' && "bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400",
                  line.type === 'added' && "bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400",
                  line.type === 'unchanged' && "bg-gray-50 dark:bg-gray-800 text-gray-500 dark:text-gray-400"
                )}
              >
                {line.type === 'removed' && '-'}
                {line.type === 'added' && '+'}
                {line.type === 'unchanged' && ' '}
              </div>

              {/* Line content */}
              <div
                className={cn(
                  "flex-1 px-3 py-1 whitespace-pre-wrap break-words",
                  line.type === 'removed' && "text-red-800 dark:text-red-200",
                  line.type === 'added' && "text-green-800 dark:text-green-200",
                  line.type === 'unchanged' && "text-gray-700 dark:text-gray-300"
                )}
              >
                {line.content || ' '}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Summary */}
      <div className="bg-gray-50 dark:bg-gray-800 px-4 py-2 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <div className="flex items-center space-x-4">
            <span className="flex items-center">
              <span className="w-2 h-2 bg-red-400 rounded-full mr-1"></span>
              {diffLines.filter(l => l.type === 'removed').length} removed
            </span>
            <span className="flex items-center">
              <span className="w-2 h-2 bg-green-400 rounded-full mr-1"></span>
              {diffLines.filter(l => l.type === 'added').length} added
            </span>
          </div>
          <div>
            Press Cmd/Ctrl+K to make more changes
          </div>
        </div>
      </div>
    </div>
  );
}
