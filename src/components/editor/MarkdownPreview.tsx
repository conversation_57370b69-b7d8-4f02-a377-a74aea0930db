import { useMemo } from 'react';
import { marked } from 'marked';
import { cn } from '../../lib/utils';

interface MarkdownPreviewProps {
  content: string;
  className?: string;
}

export function MarkdownPreview({ content, className }: MarkdownPreviewProps) {
  const htmlContent = useMemo(() => {
    if (!content) return '';

    try {
      // Configure marked for better security and features
      marked.setOptions({
        breaks: true,
        gfm: true
      });

      return marked(content);
    } catch (error) {
      console.error('Error parsing markdown:', error);
      return content;
    }
  }, [content]);

  if (!content) {
    return (
      <div className={cn("flex items-center justify-center h-full text-gray-500 dark:text-gray-400", className)}>
        <div className="text-center">
          <p className="text-lg font-medium">No content to preview</p>
          <p className="text-sm">Start writing in the editor to see a preview</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("h-full overflow-auto", className)}>
      <div
        className="prose prose-gray dark:prose-invert max-w-none p-6"
        dangerouslySetInnerHTML={{ __html: htmlContent }}
      />
    </div>
  );
}