import { useMemo } from 'react';
import { cn } from '../../lib/utils';

interface MarkdownPreviewProps {
  content: string;
  className?: string;
}

export function MarkdownPreview({ content, className }: MarkdownPreviewProps) {
  // Simple markdown parser for basic formatting
  const parseMarkdown = useMemo(() => {
    if (!content) return '';

    let html = content;

    // Headers
    html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
    html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
    html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');

    // Bold
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    html = html.replace(/__(.*?)__/g, '<strong>$1</strong>');

    // Italic
    html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');
    html = html.replace(/_(.*?)_/g, '<em>$1</em>');

    // Code blocks
    html = html.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');
    
    // Inline code
    html = html.replace(/`(.*?)`/g, '<code>$1</code>');

    // Links
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>');

    // Unordered lists
    html = html.replace(/^\s*[-*+]\s+(.*$)/gim, '<li>$1</li>');
    html = html.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');

    // Ordered lists
    html = html.replace(/^\s*\d+\.\s+(.*$)/gim, '<li>$1</li>');
    // Note: This is a simplified approach. A proper parser would handle nested lists better.

    // Blockquotes
    html = html.replace(/^>\s+(.*$)/gim, '<blockquote>$1</blockquote>');

    // Horizontal rules
    html = html.replace(/^---$/gim, '<hr>');
    html = html.replace(/^\*\*\*$/gim, '<hr>');

    // Line breaks
    html = html.replace(/\n\n/g, '</p><p>');
    html = html.replace(/\n/g, '<br>');

    // Wrap in paragraphs
    if (html && !html.startsWith('<')) {
      html = '<p>' + html + '</p>';
    }

    return html;
  }, [content]);

  if (!content) {
    return (
      <div className={cn("flex items-center justify-center h-full text-gray-500 dark:text-gray-400", className)}>
        <div className="text-center">
          <p className="text-lg font-medium mb-2">No content to preview</p>
          <p className="text-sm">Start writing in the editor to see a preview here</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("overflow-y-auto", className)}>
      <div 
        className={cn(
          "prose prose-sm max-w-none dark:prose-invert",
          "prose-headings:font-semibold prose-headings:text-gray-900 dark:text-gray-100",
          "prose-p:text-gray-900 dark:text-gray-100 prose-p:leading-relaxed",
          "prose-a:text-blue-500 hover:prose-a:text-blue-500/80",
          "prose-strong:text-gray-900 dark:text-gray-100 prose-em:text-gray-900 dark:text-gray-100",
          "prose-code:text-gray-900 dark:text-gray-100 prose-code:bg-gray-100 dark:bg-gray-800 prose-code:px-1 prose-code:py-0.5 prose-code:rounded",
          "prose-pre:bg-gray-100 dark:bg-gray-800 prose-pre:border prose-pre:border-gray-200 dark:border-gray-700",
          "prose-blockquote:border-l-blue-500 prose-blockquote:text-gray-500 dark:text-gray-400",
          "prose-hr:border-gray-200 dark:border-gray-700",
          "prose-ul:text-gray-900 dark:text-gray-100 prose-ol:text-gray-900 dark:text-gray-100 prose-li:text-gray-900 dark:text-gray-100"
        )}
        dangerouslySetInnerHTML={{ __html: parseMarkdown }}
      />
    </div>
  );
}
