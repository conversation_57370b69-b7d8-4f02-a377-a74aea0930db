import React, { useState, useEffect, useRef } from 'react';
import { Dialog, DialogContent } from '../ui/Dialog';
import { Input } from '../ui/Input';
import { Button } from '../ui/Button';
import { Loader2, <PERSON><PERSON><PERSON>, Edit, FileText, Languages, Zap } from 'lucide-react';
import { aiService } from '../../services/aiService';
import { cn } from '../../lib/utils';

interface AICommand {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  action: (text: string, instruction?: string) => Promise<string>;
  requiresInstruction?: boolean;
}

interface AICommandPaletteProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedText: string;
  onApplyEdit: (newText: string) => void;
}

export function AICommandPalette({ 
  open, 
  onOpenChange, 
  selectedText, 
  onApplyEdit 
}: AICommandPaletteProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [customInstruction, setCustomInstruction] = useState('');
  const [selectedCommand, setSelectedCommand] = useState<AICommand | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const commands: AICommand[] = [
    {
      id: 'rewrite-formal',
      name: 'Make Formal',
      description: 'Rewrite in a formal, professional tone',
      icon: <FileText className="h-4 w-4" />,
      action: async (text) => {
        const response = await aiService.rewriteText(text, 'formal');
        if (!response.success) throw new Error(response.error);
        return response.content!;
      }
    },
    {
      id: 'rewrite-casual',
      name: 'Make Casual',
      description: 'Rewrite in a casual, conversational tone',
      icon: <Edit className="h-4 w-4" />,
      action: async (text) => {
        const response = await aiService.rewriteText(text, 'casual');
        if (!response.success) throw new Error(response.error);
        return response.content!;
      }
    },
    {
      id: 'expand',
      name: 'Expand',
      description: 'Add more details and explanations',
      icon: <Zap className="h-4 w-4" />,
      action: async (text) => {
        const response = await aiService.expandText(text);
        if (!response.success) throw new Error(response.error);
        return response.content!;
      }
    },
    {
      id: 'condense',
      name: 'Condense',
      description: 'Make more concise while preserving key information',
      icon: <FileText className="h-4 w-4" />,
      action: async (text) => {
        const response = await aiService.condenseText(text);
        if (!response.success) throw new Error(response.error);
        return response.content!;
      }
    },
    {
      id: 'fix-grammar',
      name: 'Fix Grammar',
      description: 'Correct grammar, spelling, and punctuation',
      icon: <Edit className="h-4 w-4" />,
      action: async (text) => {
        const response = await aiService.fixGrammar(text);
        if (!response.success) throw new Error(response.error);
        return response.content!;
      }
    },
    {
      id: 'translate',
      name: 'Translate',
      description: 'Translate to another language',
      icon: <Languages className="h-4 w-4" />,
      requiresInstruction: true,
      action: async (text, instruction) => {
        const response = await aiService.translateText(text, instruction || 'Spanish');
        if (!response.success) throw new Error(response.error);
        return response.content!;
      }
    },
    {
      id: 'custom',
      name: 'Custom Edit',
      description: 'Provide your own editing instruction',
      icon: <Sparkles className="h-4 w-4" />,
      requiresInstruction: true,
      action: async (text, instruction) => {
        const response = await aiService.editText(text, instruction || 'Improve this text');
        if (!response.success) throw new Error(response.error);
        return response.content!;
      }
    }
  ];

  const filteredCommands = commands.filter(cmd =>
    cmd.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    cmd.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  useEffect(() => {
    if (open) {
      setSearchQuery('');
      setCustomInstruction('');
      setSelectedCommand(null);
      setResult(null);
      setError(null);
      setIsProcessing(false);
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [open]);

  const handleCommandSelect = (command: AICommand) => {
    setSelectedCommand(command);
    setError(null);
    
    if (command.requiresInstruction) {
      // Focus on instruction input
      return;
    }
    
    executeCommand(command);
  };

  const executeCommand = async (command: AICommand, instruction?: string) => {
    if (!selectedText.trim()) {
      setError('No text selected for editing');
      return;
    }

    if (!aiService.isConfigured()) {
      setError('AI service not configured. Please check your settings.');
      return;
    }

    setIsProcessing(true);
    setError(null);
    setResult(null);

    try {
      const result = await command.action(selectedText, instruction);
      setResult(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleApply = () => {
    if (result) {
      onApplyEdit(result);
      onOpenChange(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onOpenChange(false);
    } else if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (selectedCommand?.requiresInstruction && customInstruction.trim()) {
        executeCommand(selectedCommand, customInstruction);
      } else if (filteredCommands.length > 0 && !selectedCommand) {
        handleCommandSelect(filteredCommands[0]);
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] p-0">
        <div className="flex flex-col h-full">
          {/* Search Input */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <Input
              ref={inputRef}
              placeholder="Search AI commands..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              className="border-none shadow-none focus:ring-0"
            />
          </div>

          {/* Commands List */}
          {!selectedCommand && (
            <div className="flex-1 overflow-y-auto">
              {filteredCommands.length === 0 ? (
                <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                  No commands found
                </div>
              ) : (
                <div className="p-2">
                  {filteredCommands.map((command) => (
                    <button
                      key={command.id}
                      onClick={() => handleCommandSelect(command)}
                      className={cn(
                        "w-full flex items-center space-x-3 p-3 rounded-md text-left",
                        "hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                      )}
                    >
                      <div className="text-gray-500 dark:text-gray-400">
                        {command.icon}
                      </div>
                      <div className="flex-1">
                        <div className="font-medium">{command.name}</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {command.description}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Instruction Input */}
          {selectedCommand?.requiresInstruction && !result && !isProcessing && (
            <div className="p-4 border-t border-gray-200 dark:border-gray-700">
              <div className="space-y-3">
                <div className="font-medium">{selectedCommand.name}</div>
                <Input
                  placeholder={
                    selectedCommand.id === 'translate' 
                      ? 'Enter target language (e.g., Spanish, French, German)' 
                      : 'Enter your instruction...'
                  }
                  value={customInstruction}
                  onChange={(e) => setCustomInstruction(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && customInstruction.trim()) {
                      executeCommand(selectedCommand, customInstruction);
                    }
                  }}
                />
                <div className="flex space-x-2">
                  <Button
                    onClick={() => executeCommand(selectedCommand, customInstruction)}
                    disabled={!customInstruction.trim()}
                    size="sm"
                  >
                    Execute
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setSelectedCommand(null)}
                    size="sm"
                  >
                    Back
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Processing State */}
          {isProcessing && (
            <div className="p-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-2 text-blue-600 dark:text-blue-400">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Processing...</span>
              </div>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="p-4 border-t border-gray-200 dark:border-gray-700">
              <div className="text-red-600 dark:text-red-400 text-sm">{error}</div>
              <Button
                variant="outline"
                onClick={() => {
                  setError(null);
                  setSelectedCommand(null);
                }}
                size="sm"
                className="mt-2"
              >
                Try Again
              </Button>
            </div>
          )}

          {/* Result */}
          {result && (
            <div className="p-4 border-t border-gray-200 dark:border-gray-700 max-h-60 overflow-y-auto">
              <div className="space-y-3">
                <div className="font-medium">Result:</div>
                <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md text-sm whitespace-pre-wrap">
                  {result}
                </div>
                <div className="flex space-x-2">
                  <Button onClick={handleApply} size="sm">
                    Apply
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setResult(null);
                      setSelectedCommand(null);
                    }}
                    size="sm"
                  >
                    Discard
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
