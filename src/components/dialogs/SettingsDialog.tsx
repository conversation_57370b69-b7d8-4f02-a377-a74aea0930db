import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from '../ui/Dialog';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Label } from '../ui/Label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/Select';
import { Separator } from '../ui/Separator';
import { useSettingsStore } from '../../stores/settingsStore';
import { aiService } from '../../services/aiService';
import type { AppSettings } from '../../types';
import { useState, useEffect } from 'react';

interface SettingsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function SettingsDialog({ open, onOpenChange }: SettingsDialogProps) {
  const { 
    theme, 
    updateSettings,
    aiProvider, 
    apiKey, 
    ollamaEndpoint, 
    notesDirectory, 
  } = useSettingsStore();

  const [formData, setFormData] = useState<AppSettings>({
    aiProvider,
    apiKey: apiKey || '',
    ollamaEndpoint: ollamaEndpoint || 'http://localhost:11434',
    notesDirectory: notesDirectory || '',
    theme
  });

  useEffect(() => {
    if (open) {
      setFormData({
        aiProvider,
        apiKey: apiKey || '',
        ollamaEndpoint: ollamaEndpoint || 'http://localhost:11434',
        notesDirectory: notesDirectory || '',
        theme
      });
    }
  }, [open, aiProvider, apiKey, ollamaEndpoint, notesDirectory, theme]);

  const handleSave = async () => {
    try {
      updateSettings(formData);
      aiService.updateSettings(formData);
      onOpenChange(false);
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  };

  const handleCancel = () => {
    setFormData({
      aiProvider,
      apiKey: apiKey || '',
      ollamaEndpoint: ollamaEndpoint || 'http://localhost:11434',
      notesDirectory: notesDirectory || '',
      theme
    });
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Settings</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* AI Provider Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">AI Provider</h3>
            
            <div className="space-y-2">
              <Label htmlFor="aiProvider">Provider</Label>
              <Select
                value={formData.aiProvider}
                onValueChange={(value: AppSettings['aiProvider']) => 
                  setFormData((prev: typeof formData) => ({ ...prev, aiProvider: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select AI provider" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="openai">OpenAI (GPT)</SelectItem>
                  <SelectItem value="anthropic">Anthropic (Claude)</SelectItem>
                  <SelectItem value="ollama">Ollama (Local)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* API Key for cloud providers */}
            {(formData.aiProvider === 'openai' || formData.aiProvider === 'anthropic') && (
              <div className="space-y-2">
                <Label htmlFor="apiKey">
                  API Key
                  {formData.aiProvider === 'openai' && (
                    <span className="text-sm text-gray-500 dark:text-gray-400 ml-2">
                      (Get from OpenAI Dashboard)
                    </span>
                  )}
                  {formData.aiProvider === 'anthropic' && (
                    <span className="text-sm text-gray-500 dark:text-gray-400 ml-2">
                      (Get from Anthropic Console)
                    </span>
                  )}
                </Label>
                <div className="flex space-x-2">
                  <Input
                    id="apiKey"
                    type="password"
                    value={formData.apiKey}
                    onChange={(e) => setFormData((prev: typeof formData) => ({ ...prev, apiKey: e.target.value }))}
                    placeholder={`Enter your ${formData.aiProvider === 'openai' ? 'OpenAI' : 'Anthropic'} API key`}
                  />
                </div>
              </div>
            )}

            {/* Ollama endpoint */}
            {formData.aiProvider === 'ollama' && (
              <div className="space-y-2">
                <Label htmlFor="ollamaEndpoint">Ollama Endpoint</Label>
                <Input
                  id="ollamaEndpoint"
                  value={formData.ollamaEndpoint}
                  onChange={(e) => setFormData((prev: typeof formData) => ({ ...prev, ollamaEndpoint: e.target.value }))}
                  placeholder="http://localhost:11434"
                />
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Make sure Ollama is running locally or enter a remote endpoint
                </p>
              </div>
            )}
          </div>

          <Separator />

          {/* Appearance Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Appearance</h3>
            
            <div className="space-y-2">
              <Label htmlFor="theme">Theme</Label>
              <Select
                value={formData.theme}
                onValueChange={(value: AppSettings['theme']) => 
                  setFormData((prev: typeof formData) => ({ ...prev, theme: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select theme" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">Light</SelectItem>
                  <SelectItem value="dark">Dark</SelectItem>
                  <SelectItem value="system">System</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Separator />

          {/* Storage Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Storage</h3>
            
            <div className="space-y-2">
              <Label htmlFor="notesDirectory">Notes Directory</Label>
              <Input
                id="notesDirectory"
                value={formData.notesDirectory}
                onChange={(e) => setFormData((prev: typeof formData) => ({ ...prev, notesDirectory: e.target.value }))}
                placeholder="Default: App Data/Synthesis/notes"
              />
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Leave empty to use the default location
              </p>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSave}>
            Save Settings
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
