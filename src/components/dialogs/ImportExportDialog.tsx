import { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from '../ui/Dialog';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Label } from '../ui/Label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/Select';
import { Separator } from '../ui/Separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/Tabs';
import { Upload, Download, FileText, Database, Loader2 } from 'lucide-react';
import { useNotesStore } from '../../stores/notesStore';
import { NotionService } from '../../services/notionService';
import { ObsidianService } from '../../services/obsidianService';
import type { ImportExportOptions } from '../../types';
import { open as openDialog, save as saveDialog } from '@tauri-apps/plugin-dialog';
import { writeTextFile, readTextFile } from '@tauri-apps/plugin-fs';

interface ImportExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ImportExportDialog({ open, onOpenChange }: ImportExportDialogProps) {
  const { rawNotes, summarizedNotes, createNote } = useNotesStore();
  const [activeTab, setActiveTab] = useState<'import' | 'export'>('import');
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Import state
  const [importFormat, setImportFormat] = useState<'notion' | 'obsidian' | 'markdown'>('notion');
  const [notionApiKey, setNotionApiKey] = useState('');
  const [notionDatabaseId, setNotionDatabaseId] = useState('');
  const [obsidianVaultPath, setObsidianVaultPath] = useState('');

  // Export state
  const [exportFormat, setExportFormat] = useState<'notion' | 'obsidian' | 'markdown'>('obsidian');
  const [exportOptions, setExportOptions] = useState<ImportExportOptions>({
    format: 'obsidian',
    includeMetadata: true,
    preserveStructure: true
  });

  const handleImport = async () => {
    console.log('[ImportExportDialog] handleImport called with format:', importFormat);
    setIsProcessing(true);
    setError(null);
    setResult(null);

    try {
      switch (importFormat) {
        case 'notion':
          await handleNotionImport();
          break;
        case 'obsidian':
          await handleObsidianImport();
          break;
        case 'markdown':
          await handleMarkdownImport();
          break;
      }
    } catch (err) {
      console.error('[ImportExportDialog] Import failed:', err);
      setError(err instanceof Error ? err.message : 'Import failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleExport = async () => {
    setIsProcessing(true);
    setError(null);
    setResult(null);

    try {
      switch (exportFormat) {
        case 'notion':
          await handleNotionExport();
          break;
        case 'obsidian':
          await handleObsidianExport();
          break;
        case 'markdown':
          await handleMarkdownExport();
          break;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Export failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleNotionImport = async () => {
    if (!notionApiKey || !notionDatabaseId) {
      throw new Error('Notion API key and database ID are required');
    }

    const notionService = new NotionService({ apiKey: notionApiKey });
    const isValid = await notionService.validateConnection();
    
    if (!isValid) {
      throw new Error('Invalid Notion API key or insufficient permissions');
    }

    const pages = await notionService.getPages(notionDatabaseId);
    let importedCount = 0;

    for (const page of pages) {
      const note = await notionService.importPageAsNote(page.id);
      if (note) {
        await createNote({
          ...note,
          type: 'raw',
        });
        importedCount++;
      }
    }

    setResult(`Successfully imported ${importedCount} notes from Notion`);
  };

  const handleObsidianImport = async () => {
    // Prompt user for files
    const files = await openDialog({ multiple: true, filters: [{ name: 'Markdown', extensions: ['md'] }] });
    if (!files || (Array.isArray(files) && files.length === 0)) {
      setResult('Import cancelled.');
      return;
    }
    const obsidianService = new ObsidianService();
    let importedCount = 0;
    const fileList = Array.isArray(files) ? files : [files];
    for (const filePath of fileList) {
      const content = await readTextFile(filePath);
      const note = obsidianService.obsidianFormatToNote(content, filePath);
      if (note) {
        await createNote({ ...note, type: 'raw' in note ? 'raw' : 'summary' });
        importedCount++;
      }
    }
    setResult(`Imported ${importedCount} notes from Obsidian files.`);
  };

  const handleMarkdownImport = async () => {
    console.log('[ImportExportDialog] handleMarkdownImport called');
    // Prompt user for files
    const files = await openDialog({ multiple: true, filters: [{ name: 'Markdown', extensions: ['md'] }] });
    console.log('[ImportExportDialog] Files selected:', files);
    if (!files || (Array.isArray(files) && files.length === 0)) {
      setResult('Import cancelled.');
      return;
    }
    let importedCount = 0;
    const fileList = Array.isArray(files) ? files : [files];
    for (const filePath of fileList) {
      try {
        const content = await readTextFile(filePath);
        console.log('[ImportExportDialog] Read file:', filePath, 'Content preview:', content.slice(0, 100));
        // Try to parse as Synthesis note, fallback to raw note
        let note;
        try {
          note = JSON.parse(content);
        } catch {
          note = {
            id: crypto.randomUUID(),
            title: filePath.split('/').pop()?.replace('.md', '') || 'Untitled',
            content,
            tags: [],
            createdAt: new Date(),
            updatedAt: new Date(),
            filePath,
            type: 'raw',
            sourceType: 'other',
          };
        }
        console.log('[ImportExportDialog] Calling createNote with:', note);
        await createNote(note);
        importedCount++;
      } catch (err) {
        console.error('[ImportExportDialog] Failed to import file:', filePath, err);
      }
    }
    setResult(`Imported ${importedCount} Markdown files as notes.`);
  };

  const handleNotionExport = async () => {
    if (!notionApiKey || !notionDatabaseId) {
      throw new Error('Notion API key and database ID are required');
    }

    const notionService = new NotionService({ apiKey: notionApiKey });
    const allNotes = [...rawNotes, ...summarizedNotes];
    let exportedCount = 0;

    for (const note of allNotes) {
      const pageId = await notionService.exportNoteToNotion(note, notionDatabaseId);
      if (pageId) {
        exportedCount++;
      }
    }

    setResult(`Successfully exported ${exportedCount} notes to Notion`);
  };

  const handleObsidianExport = async () => {
    const obsidianService = new ObsidianService();
    const allNotes = [...rawNotes, ...summarizedNotes];
    const vaultStructure = obsidianService.generateVaultStructure(allNotes);

    // Prompt user for export directory
    const dir = await openDialog({ directory: true, multiple: false });
    if (!dir) {
      setResult('Export cancelled.');
      return;
    }

    // Write each file
    for (const [filePath, content] of Object.entries(vaultStructure)) {
      const fullPath = `${dir}/${filePath}`;
      await writeTextFile(fullPath, content);
    }
    setResult(`Exported Obsidian vault to ${dir}`);
  };

  const handleMarkdownExport = async () => {
    // Prompt user for export directory
    const dir = await openDialog({ directory: true, multiple: false });
    if (!dir) {
      setResult('Export cancelled.');
      return;
    }
    const allNotes = [...rawNotes, ...summarizedNotes];
    for (const note of allNotes) {
      const fileName = `${note.title.replace(/[^a-zA-Z0-9-_]/g, '_')}.md`;
      const fullPath = `${dir}/${fileName}`;
      await writeTextFile(fullPath, note.content);
    }
    setResult(`Exported ${allNotes.length} notes as Markdown to ${dir}`);
  };

  const resetDialog = () => {
    setResult(null);
    setError(null);
    setIsProcessing(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Import & Export</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'import' | 'export')}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="import" className="flex items-center space-x-2">
              <Upload className="h-4 w-4" />
              <span>Import</span>
            </TabsTrigger>
            <TabsTrigger value="export" className="flex items-center space-x-2">
              <Download className="h-4 w-4" />
              <span>Export</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="import" className="space-y-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Import Format</Label>
                <Select value={importFormat} onValueChange={(value: any) => setImportFormat(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="notion">
                      <div className="flex items-center space-x-2">
                        <Database className="h-4 w-4" />
                        <span>Notion</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="obsidian">
                      <div className="flex items-center space-x-2">
                        <FileText className="h-4 w-4" />
                        <span>Obsidian Vault</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="markdown">
                      <div className="flex items-center space-x-2">
                        <FileText className="h-4 w-4" />
                        <span>Markdown Files</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {importFormat === 'notion' && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="notion-api-key">Notion API Key</Label>
                    <Input
                      id="notion-api-key"
                      type="password"
                      value={notionApiKey}
                      onChange={(e) => setNotionApiKey(e.target.value)}
                      placeholder="secret_..."
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="notion-database-id">Database ID</Label>
                    <Input
                      id="notion-database-id"
                      value={notionDatabaseId}
                      onChange={(e) => setNotionDatabaseId(e.target.value)}
                      placeholder="Database ID from Notion URL"
                    />
                  </div>
                </>
              )}

              {importFormat === 'obsidian' && (
                <div className="space-y-2">
                  <Label htmlFor="obsidian-path">Obsidian Vault Path</Label>
                  <Input
                    id="obsidian-path"
                    value={obsidianVaultPath}
                    onChange={(e) => setObsidianVaultPath(e.target.value)}
                    placeholder="/path/to/obsidian/vault"
                  />
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="export" className="space-y-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Export Format</Label>
                <Select value={exportFormat} onValueChange={(value: any) => setExportFormat(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="notion">
                      <div className="flex items-center space-x-2">
                        <Database className="h-4 w-4" />
                        <span>Notion</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="obsidian">
                      <div className="flex items-center space-x-2">
                        <FileText className="h-4 w-4" />
                        <span>Obsidian Vault</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="markdown">
                      <div className="flex items-center space-x-2">
                        <FileText className="h-4 w-4" />
                        <span>Markdown Files</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {exportFormat === 'notion' && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="export-notion-api-key">Notion API Key</Label>
                    <Input
                      id="export-notion-api-key"
                      type="password"
                      value={notionApiKey}
                      onChange={(e) => setNotionApiKey(e.target.value)}
                      placeholder="secret_..."
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="export-notion-database-id">Database ID</Label>
                    <Input
                      id="export-notion-database-id"
                      value={notionDatabaseId}
                      onChange={(e) => setNotionDatabaseId(e.target.value)}
                      placeholder="Database ID from Notion URL"
                    />
                  </div>
                </>
              )}

              <div className="space-y-2">
                <Label>Export Options</Label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={exportOptions.includeMetadata}
                      onChange={(e) => setExportOptions(prev => ({ 
                        ...prev, 
                        includeMetadata: e.target.checked 
                      }))}
                    />
                    <span className="text-sm">Include metadata (tags, dates, etc.)</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={exportOptions.preserveStructure}
                      onChange={(e) => setExportOptions(prev => ({ 
                        ...prev, 
                        preserveStructure: e.target.checked 
                      }))}
                    />
                    <span className="text-sm">Preserve folder structure</span>
                  </label>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {(result || error || isProcessing) && (
          <>
            <Separator />
            <div className="space-y-2">
              {isProcessing && (
                <div className="flex items-center space-x-2 text-blue-600 dark:text-blue-400">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Processing...</span>
                </div>
              )}
              {error && (
                <div className="text-red-600 dark:text-red-400 text-sm">{error}</div>
              )}
              {result && (
                <div className="text-green-600 dark:text-green-400 text-sm">{result}</div>
              )}
            </div>
          </>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
          {!result && !error && (
            <Button
              onClick={activeTab === 'import' ? handleImport : handleExport}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  {activeTab === 'import' ? (
                    <>
                      <Upload className="h-4 w-4 mr-2" />
                      Import
                    </>
                  ) : (
                    <>
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </>
                  )}
                </>
              )}
            </Button>
          )}
          {(result || error) && (
            <Button onClick={resetDialog}>
              Try Again
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
