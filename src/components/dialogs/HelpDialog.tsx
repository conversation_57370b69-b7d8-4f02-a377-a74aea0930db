import { Di<PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from '../ui/Dialog';
import { Separator } from '../ui/Separator';
import { useKeyboardShortcutsHelp } from '../../hooks/useKeyboardShortcuts';
import { Keyboard, Sparkles, FileText, Settings } from 'lucide-react';

interface HelpDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function HelpDialog({ open, onOpenChange }: HelpDialogProps) {
  const { shortcutGroups, formatShortcut } = useKeyboardShortcutsHelp();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Keyboard className="h-5 w-5" />
            <span>Help & Shortcuts</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Getting Started */}
          <section>
            <h3 className="text-lg font-medium mb-3 flex items-center space-x-2">
              <FileText className="h-4 w-4" />
              <span>Getting Started</span>
            </h3>
            <div className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
              <p>
                <strong>Synthesis</strong> is an AI-powered note-taking application designed for knowledge workers.
              </p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>Create raw material notes from books, articles, videos, and other sources</li>
                <li>Use AI to generate summaries and insights from your notes</li>
                <li>Edit text with AI assistance using Cmd/Ctrl+K</li>
                <li>Export to Notion or Obsidian for further processing</li>
              </ul>
            </div>
          </section>

          <Separator />

          {/* AI Features */}
          <section>
            <h3 className="text-lg font-medium mb-3 flex items-center space-x-2">
              <Sparkles className="h-4 w-4" />
              <span>AI Features</span>
            </h3>
            <div className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
              <div className="space-y-2">
                <div>
                  <strong>AI Editing (Cmd/Ctrl+K):</strong>
                  <ul className="list-disc list-inside ml-4 mt-1">
                    <li>Select text and press Cmd/Ctrl+K to open AI commands</li>
                    <li>Choose from preset commands or write custom instructions</li>
                    <li>Review changes with visual diff before applying</li>
                  </ul>
                </div>
                <div>
                  <strong>Available Commands:</strong>
                  <ul className="list-disc list-inside ml-4 mt-1">
                    <li>Make Formal/Casual - Adjust tone</li>
                    <li>Expand/Condense - Change length</li>
                    <li>Fix Grammar - Correct errors</li>
                    <li>Translate - Convert to other languages</li>
                    <li>Custom Edit - Your own instructions</li>
                  </ul>
                </div>
              </div>
            </div>
          </section>

          <Separator />

          {/* Keyboard Shortcuts */}
          <section>
            <h3 className="text-lg font-medium mb-3">Keyboard Shortcuts</h3>
            <div className="space-y-4">
              {Object.entries(shortcutGroups).map(([groupName, shortcuts]) => (
                <div key={groupName}>
                  <h4 className="font-medium text-sm text-gray-500 dark:text-gray-400 mb-2">
                    {groupName}
                  </h4>
                  <div className="space-y-1">
                    {shortcuts.map((shortcut, index) => (
                      <div key={index} className="flex items-center justify-between py-1">
                        <span className="text-sm text-gray-700 dark:text-gray-300">
                          {shortcut.description}
                        </span>
                        <kbd className="px-2 py-1 text-xs font-mono bg-gray-100 dark:bg-gray-800 rounded border">
                          {formatShortcut(shortcut)}
                        </kbd>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </section>

          <Separator />

          {/* Settings */}
          <section>
            <h3 className="text-lg font-medium mb-3 flex items-center space-x-2">
              <Settings className="h-4 w-4" />
              <span>Configuration</span>
            </h3>
            <div className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
              <div>
                <strong>AI Provider Setup:</strong>
                <ul className="list-disc list-inside ml-4 mt-1">
                  <li><strong>OpenAI:</strong> Get API key from platform.openai.com</li>
                  <li><strong>Anthropic:</strong> Get API key from console.anthropic.com</li>
                  <li><strong>Ollama:</strong> Install locally and ensure it's running</li>
                </ul>
              </div>
              <div>
                <strong>Import/Export:</strong>
                <ul className="list-disc list-inside ml-4 mt-1">
                  <li><strong>Notion:</strong> Requires API key and database ID</li>
                  <li><strong>Obsidian:</strong> Exports vault-compatible markdown files</li>
                  <li><strong>Markdown:</strong> Standard markdown with frontmatter</li>
                </ul>
              </div>
            </div>
          </section>

          <Separator />

          {/* Tips */}
          <section>
            <h3 className="text-lg font-medium mb-3">Tips & Best Practices</h3>
            <div className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
              <ul className="list-disc list-inside space-y-1">
                <li>Use descriptive titles for better organization</li>
                <li>Add tags to categorize and find notes easily</li>
                <li>Include source URLs for raw material notes</li>
                <li>Use AI editing to improve clarity and style</li>
                <li>Create summaries to synthesize multiple sources</li>
                <li>Export regularly to backup your work</li>
              </ul>
            </div>
          </section>
        </div>
      </DialogContent>
    </Dialog>
  );
}
