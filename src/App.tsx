import { useEffect } from 'react';
import { ErrorBoundary } from './components/common/ErrorBoundary';
import { Sidebar } from './components/layout/Sidebar';
import { MainPanel } from './components/layout/MainPanel';
import { useUIStore } from './stores/uiStore';
import { useNotesStore } from './stores/notesStore';
import { useSettingsStore } from './stores/settingsStore';
import { useKeyboardShortcuts } from './hooks/useKeyboardShortcuts';
import { cn } from './lib/utils';

function App() {
  const { sidebarWidth, sidebarCollapsed } = useUIStore();
  const { loadNotes } = useNotesStore();
  const { theme } = useSettingsStore();

  // Initialize keyboard shortcuts
  useKeyboardShortcuts();

  useEffect(() => {
    // Apply theme
    const root = window.document.documentElement;
    root.classList.remove('light', 'dark');

    if (theme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      root.classList.add(systemTheme);
    } else {
      root.classList.add(theme);
    }
  }, [theme]);

  useEffect(() => {
    // Load notes on app start
    loadNotes();
  }, [loadNotes]);

  return (
    <ErrorBoundary>
      <div className="h-screen flex bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">
        {/* Sidebar */}
        <div
          className={cn(
            "transition-all duration-200 ease-in-out",
            sidebarCollapsed ? "w-12" : "w-80"
          )}
          style={{
            width: sidebarCollapsed ? '48px' : `${sidebarWidth}px`
          }}
        >
          <Sidebar />
        </div>

        {/* Resize handle */}
        {!sidebarCollapsed && (
          <div
            className="w-1 bg-border hover:bg-blue-500/20 cursor-col-resize transition-colors"
            onMouseDown={(e) => {
              e.preventDefault();
              const startX = e.clientX;
              const startWidth = sidebarWidth;

              const handleMouseMove = (e: MouseEvent) => {
                const newWidth = startWidth + (e.clientX - startX);
                useUIStore.getState().setSidebarWidth(newWidth);
              };

              const handleMouseUp = () => {
                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);
              };

              document.addEventListener('mousemove', handleMouseMove);
              document.addEventListener('mouseup', handleMouseUp);
            }}
          />
        )}

        {/* Main content */}
        <MainPanel className="flex-1" />
      </div>
    </ErrorBoundary>
  );
}

export default App;
