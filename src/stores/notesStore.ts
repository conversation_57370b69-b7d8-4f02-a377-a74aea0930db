import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { RawMaterialNote, SummarizedNote, CreateNoteParams, UpdateNoteParams } from '../types';
import { notesService } from '../services/notesService';

interface NotesState {
  rawNotes: RawMaterialNote[];
  summarizedNotes: SummarizedNote[];
  isLoading: boolean;
  error: string | null;

  // Actions
  loadNotes: () => Promise<void>;
  createNote: (params: CreateNoteParams) => Promise<string>;
  updateNote: (params: UpdateNoteParams) => Promise<void>;
  deleteNote: (id: string) => Promise<void>;
  searchNotes: (query: string) => (RawMaterialNote | SummarizedNote)[];
  getNoteById: (id: string) => RawMaterialNote | SummarizedNote | null;
  getNotesWithTag: (tag: string) => (RawMaterialNote | SummarizedNote)[];
  getAllTags: () => string[];
}

export const useNotesStore = create<NotesState>()(
  devtools(
    persist(
      (set, get) => ({
        rawNotes: [],
        summarizedNotes: [],
        isLoading: false,
        error: null,

        loadNotes: async () => {
          set({ isLoading: true, error: null });
          try {
            const { rawNotes, summarizedNotes } = await notesService.loadAllNotes();
            set({
              rawNotes,
              summarizedNotes,
              isLoading: false
            });
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to load notes',
              isLoading: false
            });
          }
        },

        createNote: async (params: CreateNoteParams): Promise<string> => {
          try {
            const id = await notesService.createNote(params);

            // Reload notes to get the updated state
            const { rawNotes, summarizedNotes } = await notesService.loadAllNotes();
            set({ rawNotes, summarizedNotes });

            return id;
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to create note'
            });
            throw error;
          }
        },

        updateNote: async (params: UpdateNoteParams) => {
          try {
            await notesService.updateNote(params);

            // Reload notes to get the updated state
            const { rawNotes, summarizedNotes } = await notesService.loadAllNotes();
            set({ rawNotes, summarizedNotes });
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to update note'
            });
            throw error;
          }
        },

        deleteNote: async (id: string) => {
          try {
            await notesService.deleteNote(id);

            // Reload notes to get the updated state
            const { rawNotes, summarizedNotes } = await notesService.loadAllNotes();
            set({ rawNotes, summarizedNotes });
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to delete note'
            });
            throw error;
          }
        },

        searchNotes: (query: string) => {
          const { rawNotes, summarizedNotes } = get();
          const allNotes = [...rawNotes, ...summarizedNotes];

          if (!query.trim()) return allNotes;

          const searchTerm = query.toLowerCase();
          return allNotes.filter(note =>
            note.title.toLowerCase().includes(searchTerm) ||
            note.content.toLowerCase().includes(searchTerm) ||
            note.tags.some(tag => tag.toLowerCase().includes(searchTerm))
          );
        },

        getNoteById: (id: string) => {
          const { rawNotes, summarizedNotes } = get();
          return [...rawNotes, ...summarizedNotes].find(note => note.id === id) || null;
        },

        getNotesWithTag: (tag: string) => {
          const { rawNotes, summarizedNotes } = get();
          const allNotes = [...rawNotes, ...summarizedNotes];
          return allNotes.filter(note => note.tags.includes(tag));
        },

        getAllTags: () => {
          const { rawNotes, summarizedNotes } = get();
          const allNotes = [...rawNotes, ...summarizedNotes];
          const tagSet = new Set<string>();
          allNotes.forEach(note => {
            note.tags.forEach(tag => tagSet.add(tag));
          });
          return Array.from(tagSet).sort();
        }
      }),
      {
        name: 'notes-storage',
        partialize: (state) => ({
          rawNotes: state.rawNotes,
          summarizedNotes: state.summarizedNotes
        })
      }
    ),
    { name: 'notes-store' }
  )
);
