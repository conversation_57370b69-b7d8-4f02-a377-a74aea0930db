import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { UIState, EditorState, AIEditSuggestion } from '../types';

interface UIStoreState extends UIState {
  editorState: EditorState;
  aiSuggestions: AIEditSuggestion[];
  
  // UI Actions
  setSidebarWidth: (width: number) => void;
  toggleSidebar: () => void;
  setCurrentView: (view: UIState['currentView']) => void;
  setSelectedNote: (noteId: string | null) => void;
  setSearchQuery: (query: string) => void;
  setSearching: (searching: boolean) => void;
  toggleSettings: () => void;
  toggleCommandPalette: () => void;
  
  // Editor Actions
  setEditorContent: (content: string) => void;
  setEditorSelection: (selection: EditorState['selection']) => void;
  markEditorDirty: (dirty: boolean) => void;
  markEditorSaved: () => void;
  
  // AI Suggestions Actions
  addAISuggestion: (suggestion: Omit<AIEditSuggestion, 'id' | 'applied'>) => void;
  applyAISuggestion: (suggestionId: string) => void;
  dismissAISuggestion: (suggestionId: string) => void;
  clearAISuggestions: () => void;
}

const defaultUIState: UIState = {
  sidebarWidth: 300,
  sidebarCollapsed: false,
  currentView: 'split',
  selectedNoteId: null,
  searchQuery: '',
  isSearching: false,
  showSettings: false,
  commandPaletteOpen: false
};

const defaultEditorState: EditorState = {
  content: '',
  selection: null,
  isDirty: false,
  lastSaved: null
};

export const useUIStore = create<UIStoreState>()(
  devtools(
    persist(
      (set) => ({
        ...defaultUIState,
        editorState: defaultEditorState,
        aiSuggestions: [],

        // UI Actions
        setSidebarWidth: (width: number) => {
          set({ sidebarWidth: Math.max(200, Math.min(600, width)) });
        },

        toggleSidebar: () => {
          set(state => ({ sidebarCollapsed: !state.sidebarCollapsed }));
        },

        setCurrentView: (view: UIState['currentView']) => {
          set({ currentView: view });
        },

        setSelectedNote: (noteId: string | null) => {
          set({ selectedNoteId: noteId });
        },

        setSearchQuery: (query: string) => {
          set({ searchQuery: query });
        },

        setSearching: (searching: boolean) => {
          set({ isSearching: searching });
        },

        toggleSettings: () => {
          set(state => ({ showSettings: !state.showSettings }));
        },

        toggleCommandPalette: () => {
          set(state => ({ commandPaletteOpen: !state.commandPaletteOpen }));
        },

        // Editor Actions
        setEditorContent: (content: string) => {
          set(state => ({
            editorState: {
              ...state.editorState,
              content,
              isDirty: content !== state.editorState.content
            }
          }));
        },

        setEditorSelection: (selection: EditorState['selection']) => {
          set(state => ({
            editorState: {
              ...state.editorState,
              selection
            }
          }));
        },

        markEditorDirty: (dirty: boolean) => {
          set(state => ({
            editorState: {
              ...state.editorState,
              isDirty: dirty
            }
          }));
        },

        markEditorSaved: () => {
          set(state => ({
            editorState: {
              ...state.editorState,
              isDirty: false,
              lastSaved: new Date()
            }
          }));
        },

        // AI Suggestions Actions
        addAISuggestion: (suggestion: Omit<AIEditSuggestion, 'id' | 'applied'>) => {
          const newSuggestion: AIEditSuggestion = {
            ...suggestion,
            id: crypto.randomUUID(),
            applied: false
          };
          
          set(state => ({
            aiSuggestions: [...state.aiSuggestions, newSuggestion]
          }));
        },

        applyAISuggestion: (suggestionId: string) => {
          set(state => {
            const suggestion = state.aiSuggestions.find(s => s.id === suggestionId);
            if (!suggestion) return state;

            return {
              aiSuggestions: state.aiSuggestions.map(s =>
                s.id === suggestionId ? { ...s, applied: true } : s
              ),
              editorState: {
                ...state.editorState,
                content: state.editorState.content.replace(
                  suggestion.originalText,
                  suggestion.suggestedText
                ),
                isDirty: true
              }
            };
          });
        },

        dismissAISuggestion: (suggestionId: string) => {
          set(state => ({
            aiSuggestions: state.aiSuggestions.filter(s => s.id !== suggestionId)
          }));
        },

        clearAISuggestions: () => {
          set({ aiSuggestions: [] });
        },

        // Keyboard shortcuts
        handleKeyboardShortcut: (key: string, metaKey: boolean, ctrlKey: boolean) => {
          if ((metaKey || ctrlKey) && key === 'k') {
            set(state => ({ commandPaletteOpen: !state.commandPaletteOpen }));
            return true;
          }
          return false;
        }
      }),
      {
        name: 'ui-storage',
        partialize: (state) => ({
          sidebarWidth: state.sidebarWidth,
          sidebarCollapsed: state.sidebarCollapsed,
          currentView: state.currentView
        })
      }
    ),
    { name: 'ui-store' }
  )
);
