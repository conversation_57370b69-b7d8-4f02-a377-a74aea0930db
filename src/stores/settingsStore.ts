import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { AppSettings } from '../types';
import { aiService } from '../services/aiService';

interface SettingsState extends AppSettings {
  // Actions
  updateSettings: (settings: Partial<AppSettings>) => void;
  resetSettings: () => void;
  validateApiKey: (provider: AppSettings['aiProvider'], apiKey: string) => Promise<boolean>;
}

const defaultSettings: AppSettings = {
  aiProvider: 'ollama',
  apiKey: '',
  ollamaEndpoint: 'http://localhost:11434',
  notesDirectory: '~/Downloads/Notes',
  theme: 'system'
};

export const useSettingsStore = create<SettingsState>()(
  devtools(
    persist(
      (set, get) => ({
        ...defaultSettings,

        updateSettings: (newSettings: Partial<AppSettings>) => {
          set(state => {
            let updatedSettings = {
              ...state,
              ...newSettings
            };
            // Update AI service with new settings
            aiService.updateSettings(updatedSettings);
            // Always update fileService notes directory if changed
            if (newSettings.notesDirectory) {
              import('../services/fileService').then(({ FileService }) => {
                const fileService = new FileService();
                fileService.setNotesDirectory(updatedSettings.notesDirectory!);
              });
            }
            console.log('[SettingsStore] Updated settings:', updatedSettings);
            return updatedSettings;
          });
        },

        resetSettings: () => {
          set(defaultSettings);
        },

        validateApiKey: async (provider: AppSettings['aiProvider'], apiKey: string): Promise<boolean> => {
          try {
            switch (provider) {
              case 'openai':
                // TODO: Implement OpenAI API validation
                return apiKey.startsWith('sk-') && apiKey.length > 20;
              
              case 'anthropic':
                // TODO: Implement Anthropic API validation
                return apiKey.startsWith('sk-ant-') && apiKey.length > 20;
              
              case 'ollama':
                // TODO: Implement Ollama endpoint validation
                const endpoint = get().ollamaEndpoint;
                try {
                  const response = await fetch(`${endpoint}/api/tags`);
                  return response.ok;
                } catch {
                  return false;
                }
              
              default:
                return false;
            }
          } catch (error) {
            console.error('API key validation error:', error);
            return false;
          }
        }
      }),
      {
        name: 'settings-storage'
      }
    ),
    { name: 'settings-store' }
  )
);
