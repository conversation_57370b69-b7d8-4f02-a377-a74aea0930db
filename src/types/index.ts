export interface RawMaterialNote {
  id: string;
  title: string;
  content: string; // Markdown format
  sourceType: 'book' | 'youtube_video' | 'article' | 'podcast' | 'other';
  sourceUrl?: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  filePath: string; // Path to the .md file
}

export interface SummarizedNote {
  id: string;
  title: string;
  content: string; // Markdown format
  linkedRawNotes: string[]; // Array of RawMaterialNote IDs
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  filePath: string; // Path to the .md file
}

export interface AppSettings {
  aiProvider: 'openai' | 'anthropic' | 'ollama';
  apiKey?: string; // For cloud providers
  ollamaEndpoint?: string; // For local LLM (default: http://localhost:11434)
  notesDirectory: string; // Where to store .md files
  theme: 'light' | 'dark' | 'system';
}

export interface AICommand {
  id: string;
  name: string;
  description: string;
  prompt: string;
  shortcut?: string;
}

export interface AIResponse {
  success: boolean;
  content?: string;
  error?: string;
}

export interface FileSystemNode {
  name: string;
  path: string;
  isDirectory: boolean;
  children?: FileSystemNode[];
}

export interface SearchResult {
  note: RawMaterialNote | SummarizedNote;
  matches: {
    field: 'title' | 'content' | 'tags';
    text: string;
    start: number;
    end: number;
  }[];
}

export type NoteType = 'raw' | 'summary';

export interface CreateNoteParams {
  title: string;
  content?: string;
  type: NoteType;
  sourceType?: RawMaterialNote['sourceType'];
  sourceUrl?: string;
  tags?: string[];
  linkedRawNotes?: string[];
}

export interface UpdateNoteParams {
  id: string;
  title?: string;
  content?: string;
  tags?: string[];
  sourceUrl?: string;
  linkedRawNotes?: string[];
}

export interface ImportExportOptions {
  format: 'notion' | 'obsidian' | 'markdown';
  includeMetadata: boolean;
  preserveStructure: boolean;
}

export interface NotionPage {
  id: string;
  title: string;
  content: any; // Notion block structure
  properties: Record<string, any>;
  createdTime: string;
  lastEditedTime: string;
}

export interface UIState {
  sidebarWidth: number;
  sidebarCollapsed: boolean;
  currentView: 'editor' | 'preview' | 'split';
  selectedNoteId: string | null;
  searchQuery: string;
  isSearching: boolean;
  showSettings: boolean;
  commandPaletteOpen: boolean;
}

export interface EditorState {
  content: string;
  selection: {
    start: number;
    end: number;
  } | null;
  isDirty: boolean;
  lastSaved: Date | null;
}

export interface AIEditSuggestion {
  id: string;
  originalText: string;
  suggestedText: string;
  explanation?: string;
  applied: boolean;
}
