import type { RawMaterialNote, SummarizedNote } from '../types';
import { addFrontmatter, extractFrontmatter, sanitizeFileName } from '../lib/utils';

export interface ObsidianVault {
  name: string;
  path: string;
}

export class ObsidianService {
  /**
   * Convert a Synthesis note to Obsidian-compatible format
   */
  noteToObsidianFormat(note: RawMaterialNote | SummarizedNote): string {
    const isRawNote = 'sourceType' in note;
    
    // Obsidian frontmatter
    const frontmatter: Record<string, any> = {
      id: note.id,
      title: note.title,
      tags: note.tags,
      created: note.createdAt.toISOString(),
      modified: note.updatedAt.toISOString(),
      synthesis_type: isRawNote ? 'raw' : 'summary'
    };

    if (isRawNote) {
      const rawNote = note as RawMaterialNote;
      frontmatter.source_type = rawNote.sourceType;
      if (rawNote.sourceUrl) {
        frontmatter.source_url = rawNote.sourceUrl;
      }
    } else {
      const summaryNote = note as SummarizedNote;
      if (summaryNote.linkedRawNotes.length > 0) {
        frontmatter.linked_notes = summaryNote.linkedRawNotes;
      }
    }

    // Convert content to Obsidian-style links
    let content = note.content;
    
    // Convert internal links (if any) to Obsidian format
    // This is a placeholder for more sophisticated link conversion
    content = this.convertLinksToObsidian(content);

    return addFrontmatter(content, frontmatter);
  }

  /**
   * Convert Obsidian note to Synthesis format
   */
  obsidianFormatToNote(content: string, filePath: string): RawMaterialNote | SummarizedNote | null {
    const { frontmatter, content: noteContent } = extractFrontmatter(content);

    if (!frontmatter.id || !frontmatter.title) {
      // Generate missing metadata
      const fileName = filePath.split('/').pop()?.replace('.md', '') || 'Untitled';
      frontmatter.id = frontmatter.id || crypto.randomUUID();
      frontmatter.title = frontmatter.title || fileName;
    }

    const baseNote = {
      id: frontmatter.id,
      title: frontmatter.title,
      content: this.convertLinksFromObsidian(noteContent),
      tags: Array.isArray(frontmatter.tags) ? frontmatter.tags : [],
      createdAt: frontmatter.created ? new Date(frontmatter.created) : new Date(),
      updatedAt: frontmatter.modified ? new Date(frontmatter.modified) : new Date(),
      filePath
    };

    // Determine note type
    const synthesisType = frontmatter.synthesis_type;
    const hasSourceType = frontmatter.source_type;

    if (synthesisType === 'raw' || hasSourceType) {
      return {
        ...baseNote,
        sourceType: frontmatter.source_type || 'other',
        sourceUrl: frontmatter.source_url
      } as RawMaterialNote;
    } else {
      return {
        ...baseNote,
        linkedRawNotes: Array.isArray(frontmatter.linked_notes) 
          ? frontmatter.linked_notes 
          : []
      } as SummarizedNote;
    }
  }

  /**
   * Convert internal links to Obsidian format [[Note Title]]
   */
  private convertLinksToObsidian(content: string): string {
    // Convert markdown links that might be internal references
    // This is a simplified implementation
    return content.replace(/\[([^\]]+)\]\(([^)]+)\)/g, (match, text, url) => {
      // If URL looks like an internal reference, convert to Obsidian link
      if (!url.startsWith('http') && !url.startsWith('/') && url.endsWith('.md')) {
        const noteName = url.replace('.md', '');
        return `[[${noteName}|${text}]]`;
      }
      return match;
    });
  }

  /**
   * Convert Obsidian links to standard markdown
   */
  private convertLinksFromObsidian(content: string): string {
    // Convert [[Note Title]] to [Note Title](Note Title.md)
    return content.replace(/\[\[([^\]|]+)(\|([^\]]+))?\]\]/g, (_match, noteName, _, displayText) => {
      const linkText = displayText || noteName;
      const fileName = sanitizeFileName(noteName) + '.md';
      return `[${linkText}](${fileName})`;
    });
  }

  /**
   * Generate Obsidian-compatible filename
   */
  generateObsidianFileName(note: RawMaterialNote | SummarizedNote): string {
    const sanitized = sanitizeFileName(note.title);
    const isRawNote = 'sourceType' in note;
    const prefix = isRawNote ? 'raw' : 'summary';
    
    // Include date for uniqueness
    const date = note.createdAt.toISOString().split('T')[0];
    
    return `${prefix}-${date}-${sanitized}.md`;
  }

  /**
   * Create Obsidian vault structure
   */
  generateVaultStructure(notes: (RawMaterialNote | SummarizedNote)[]): {
    [filePath: string]: string;
  } {
    const vaultFiles: { [filePath: string]: string } = {};

    // Create folder structure
    const rawNotes = notes.filter(note => 'sourceType' in note) as RawMaterialNote[];
    const summaryNotes = notes.filter(note => !('sourceType' in note)) as SummarizedNote[];

    // Raw notes in "Sources" folder
    rawNotes.forEach(note => {
      const fileName = this.generateObsidianFileName(note);
      const filePath = `Sources/${fileName}`;
      vaultFiles[filePath] = this.noteToObsidianFormat(note);
    });

    // Summary notes in "Summaries" folder
    summaryNotes.forEach(note => {
      const fileName = this.generateObsidianFileName(note);
      const filePath = `Summaries/${fileName}`;
      vaultFiles[filePath] = this.noteToObsidianFormat(note);
    });

    // Create index files
    vaultFiles['README.md'] = this.generateVaultReadme(notes);
    vaultFiles['Sources/README.md'] = this.generateSourcesIndex(rawNotes);
    vaultFiles['Summaries/README.md'] = this.generateSummariesIndex(summaryNotes);

    return vaultFiles;
  }

  private generateVaultReadme(notes: (RawMaterialNote | SummarizedNote)[]): string {
    const rawCount = notes.filter(note => 'sourceType' in note).length;
    const summaryCount = notes.filter(note => !('sourceType' in note)).length;

    return `# Synthesis Vault

This vault contains notes exported from Synthesis.

## Structure

- **Sources/**: Raw material notes (${rawCount} notes)
- **Summaries/**: Summary notes (${summaryCount} notes)

## Tags

${this.generateTagIndex(notes)}

## Recent Notes

${notes
  .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
  .slice(0, 10)
  .map(note => `- [[${note.title}]]`)
  .join('\n')}

---
*Exported from Synthesis on ${new Date().toISOString().split('T')[0]}*
`;
  }

  private generateSourcesIndex(notes: RawMaterialNote[]): string {
    const bySourceType = notes.reduce((acc, note) => {
      if (!acc[note.sourceType]) acc[note.sourceType] = [];
      acc[note.sourceType].push(note);
      return acc;
    }, {} as Record<string, RawMaterialNote[]>);

    let content = '# Source Materials\n\n';

    Object.entries(bySourceType).forEach(([sourceType, typeNotes]) => {
      content += `## ${sourceType.charAt(0).toUpperCase() + sourceType.slice(1)}\n\n`;
      typeNotes.forEach(note => {
        content += `- [[${note.title}]]`;
        if (note.sourceUrl) {
          content += ` - [Source](${note.sourceUrl})`;
        }
        content += '\n';
      });
      content += '\n';
    });

    return content;
  }

  private generateSummariesIndex(notes: SummarizedNote[]): string {
    let content = '# Summaries\n\n';

    notes.forEach(note => {
      content += `## [[${note.title}]]\n\n`;
      if (note.linkedRawNotes.length > 0) {
        content += 'Based on:\n';
        note.linkedRawNotes.forEach(linkedId => {
          content += `- Linked note: ${linkedId}\n`;
        });
        content += '\n';
      }
    });

    return content;
  }

  private generateTagIndex(notes: (RawMaterialNote | SummarizedNote)[]): string {
    const tagCounts = notes.reduce((acc, note) => {
      note.tags.forEach(tag => {
        acc[tag] = (acc[tag] || 0) + 1;
      });
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(tagCounts)
      .sort(([, a], [, b]) => b - a)
      .map(([tag, count]) => `- #${tag} (${count})`)
      .join('\n');
  }

  /**
   * Validate Obsidian vault compatibility
   */
  validateObsidianCompatibility(content: string): {
    isValid: boolean;
    issues: string[];
    suggestions: string[];
  } {
    const issues: string[] = [];
    const suggestions: string[] = [];

    // Check for unsupported markdown features
    if (content.includes('```mermaid')) {
      suggestions.push('Mermaid diagrams detected - consider using Obsidian Mermaid plugin');
    }

    if (content.includes('$$')) {
      suggestions.push('LaTeX math detected - ensure MathJax plugin is enabled in Obsidian');
    }

    // Check for problematic characters in links
    const problematicLinks = content.match(/\[\[([^\]]*[<>:"/\\|?*].*?)\]\]/g);
    if (problematicLinks) {
      issues.push('Links contain characters that may cause issues in Obsidian file system');
    }

    // Check frontmatter format
    const { frontmatter } = extractFrontmatter(content);
    if (Object.keys(frontmatter).length === 0 && content.startsWith('---')) {
      issues.push('Frontmatter format may not be valid YAML');
    }

    return {
      isValid: issues.length === 0,
      issues,
      suggestions
    };
  }
}
