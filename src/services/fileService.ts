import { invoke } from '@tauri-apps/api/core';
import { readTextFile, writeTextFile, exists, mkdir, readDir } from '@tauri-apps/plugin-fs';
import { join, appDataDir } from '@tauri-apps/api/path';
import type { RawMaterialNote, SummarizedNote, FileSystemNode } from '../types';
import { addFrontmatter, extractFrontmatter, sanitizeFileName } from '../lib/utils';

export class FileService {
  private notesDir: string = '';

  async initialize(): Promise<void> {
    try {
      const appData = await appDataDir();
      this.notesDir = await join(appData, 'synthesis', 'notes');
      console.log('[FileService] Initialized notesDir:', this.notesDir);
      
      // Create directories if they don't exist
      await this.ensureDirectoryExists(this.notesDir);
      await this.ensureDirectoryExists(await join(this.notesDir, 'raw'));
      await this.ensureDirectoryExists(await join(this.notesDir, 'summaries'));
    } catch (error) {
      console.error('[FileService] Failed to initialize:', error);
      throw error;
    }
  }

  private async ensureDirectoryExists(path: string): Promise<void> {
    try {
      const dirExists = await exists(path);
      if (!dirExists) {
        await mkdir(path, { recursive: true });
      }
    } catch (error) {
      console.error(`Failed to create directory ${path}:`, error);
      throw error;
    }
  }

  async saveNote(note: RawMaterialNote | SummarizedNote): Promise<RawMaterialNote | SummarizedNote> {
    console.log('[FileService] saveNote called with:', note);
    console.log('[FileService] Current notesDir:', this.notesDir);
    try {
      const isRawNote = 'sourceType' in note;
      const subDir = isRawNote ? 'raw' : 'summaries';
      const fileName = `${sanitizeFileName(note.title)}-${note.id}.md`;
      const filePath = await join(this.notesDir, subDir, fileName);
      console.log('[FileService] Saving note to:', filePath);

      // Prepare frontmatter
      const frontmatter: Record<string, any> = {
        id: note.id,
        title: note.title,
        tags: note.tags,
        createdAt: note.createdAt.toISOString(),
        updatedAt: note.updatedAt.toISOString(),
      };

      if (isRawNote) {
        const rawNote = note as RawMaterialNote;
        frontmatter.sourceType = rawNote.sourceType;
        if (rawNote.sourceUrl) {
          frontmatter.sourceUrl = rawNote.sourceUrl;
        }
      } else {
        const summaryNote = note as SummarizedNote;
        frontmatter.linkedRawNotes = summaryNote.linkedRawNotes;
      }

      const fileContent = addFrontmatter(note.content, frontmatter);
      await writeTextFile(filePath, fileContent);

      // Update the note's file path
      note.filePath = filePath;
      return note;
    } catch (error) {
      if (error instanceof Error) {
        console.error('[FileService] Failed to save note:', error, error.stack, note);
      } else {
        console.error('[FileService] Failed to save note:', error, note);
      }
      throw error;
    }
  }

  async loadNote(filePath: string): Promise<RawMaterialNote | SummarizedNote | null> {
    console.log('[FileService] loadNote called with:', filePath);
    try {
      const fileExists = await exists(filePath);
      if (!fileExists) {
        console.warn('[FileService] File does not exist:', filePath);
        return null;
      }

      const content = await readTextFile(filePath);
      console.log('[FileService] Read file content:', content.slice(0, 100));
      const { frontmatter, content: noteContent } = extractFrontmatter(content);

      if (!frontmatter.id || !frontmatter.title) {
        console.warn(`Invalid note format in ${filePath}`);
        return null;
      }

      const baseNote = {
        id: frontmatter.id,
        title: frontmatter.title,
        content: noteContent,
        tags: Array.isArray(frontmatter.tags) ? frontmatter.tags : [],
        createdAt: new Date(frontmatter.createdAt || Date.now()),
        updatedAt: new Date(frontmatter.updatedAt || Date.now()),
        filePath,
      };

      // Determine if it's a raw note or summary based on frontmatter
      if (frontmatter.sourceType) {
        return {
          ...baseNote,
          sourceType: frontmatter.sourceType,
          sourceUrl: frontmatter.sourceUrl,
        } as RawMaterialNote;
      } else {
        return {
          ...baseNote,
          linkedRawNotes: Array.isArray(frontmatter.linkedRawNotes) 
            ? frontmatter.linkedRawNotes 
            : [],
        } as SummarizedNote;
      }
    } catch (error) {
      if (error instanceof Error) {
        console.error('[FileService] Failed to load note:', error, error.stack, filePath);
      } else {
        console.error('[FileService] Failed to load note:', error, filePath);
      }
      return null;
    }
  }

  async loadAllNotes(): Promise<{
    rawNotes: RawMaterialNote[];
    summarizedNotes: SummarizedNote[];
  }> {
    const rawNotes: RawMaterialNote[] = [];
    const summarizedNotes: SummarizedNote[] = [];

    try {
      // Load raw notes
      const rawDir = await join(this.notesDir, 'raw');
      const rawFiles = await this.getMarkdownFiles(rawDir);
      
      for (const file of rawFiles) {
        const note = await this.loadNote(file.path);
        if (note && 'sourceType' in note) {
          rawNotes.push(note as RawMaterialNote);
        }
      }

      // Load summary notes
      const summariesDir = await join(this.notesDir, 'summaries');
      const summaryFiles = await this.getMarkdownFiles(summariesDir);
      
      for (const file of summaryFiles) {
        const note = await this.loadNote(file.path);
        if (note && !('sourceType' in note)) {
          summarizedNotes.push(note as SummarizedNote);
        }
      }
    } catch (error) {
      console.error('Failed to load notes:', error);
    }

    return { rawNotes, summarizedNotes };
  }

  private async getMarkdownFiles(dirPath: string): Promise<FileSystemNode[]> {
    try {
      const dirExists = await exists(dirPath);
      if (!dirExists) {
        return [];
      }

      const entries = await readDir(dirPath);
      return entries
        .filter(entry => !entry.isDirectory && entry.name.endsWith('.md'))
        .map(entry => ({
          name: entry.name,
          path: `${dirPath}/${entry.name}`,
          isDirectory: false,
        }));
    } catch (error) {
      console.error(`Failed to read directory ${dirPath}:`, error);
      return [];
    }
  }

  async deleteNote(filePath: string): Promise<void> {
    console.log('[FileService] deleteNote called with:', filePath);
    try {
      const fileExists = await exists(filePath);
      if (fileExists) {
        await invoke('plugin:fs|remove_file', { path: filePath });
        console.log('[FileService] Deleted file:', filePath);
      } else {
        console.warn('[FileService] Tried to delete non-existent file:', filePath);
      }
    } catch (error) {
      if (error instanceof Error) {
        console.error('[FileService] Failed to delete note:', error, error.stack, filePath);
      } else {
        console.error('[FileService] Failed to delete note:', error, filePath);
      }
      throw error;
    }
  }

  async getNotesDirectory(): Promise<string> {
    return this.notesDir;
  }

  async setNotesDirectory(newPath: string): Promise<void> {
    try {
      let resolvedPath = newPath;
      if (resolvedPath.startsWith('~')) {
        resolvedPath = (process.env.HOME || '') + resolvedPath.slice(1);
      }
      console.log('[FileService] Setting notesDir to (expanded):', resolvedPath);
      await this.ensureDirectoryExists(resolvedPath);
      await this.ensureDirectoryExists(await join(resolvedPath, 'raw'));
      await this.ensureDirectoryExists(await join(resolvedPath, 'summaries'));
      this.notesDir = resolvedPath;
    } catch (error) {
      console.error('[FileService] Failed to set notes directory:', error);
      throw error;
    }
  }

  async exportNote(note: RawMaterialNote | SummarizedNote, exportPath: string): Promise<void> {
    try {
      const isRawNote = 'sourceType' in note;
      const frontmatter: Record<string, any> = {
        id: note.id,
        title: note.title,
        tags: note.tags,
        createdAt: note.createdAt.toISOString(),
        updatedAt: note.updatedAt.toISOString(),
      };

      if (isRawNote) {
        const rawNote = note as RawMaterialNote;
        frontmatter.sourceType = rawNote.sourceType;
        if (rawNote.sourceUrl) {
          frontmatter.sourceUrl = rawNote.sourceUrl;
        }
      } else {
        const summaryNote = note as SummarizedNote;
        frontmatter.linkedRawNotes = summaryNote.linkedRawNotes;
      }

      const fileContent = addFrontmatter(note.content, frontmatter);
      await writeTextFile(exportPath, fileContent);
    } catch (error) {
      console.error('Failed to export note:', error);
      throw error;
    }
  }

  async importNote(filePath: string): Promise<RawMaterialNote | SummarizedNote | null> {
    console.log('[FileService] importNote called with:', filePath);
    return this.loadNote(filePath);
  }
}
