import { FileService } from './fileService';
import { DatabaseService } from './databaseService';
import type { RawMaterialNote, SummarizedNote, CreateNoteParams, UpdateNoteParams } from '../types';

export class NotesService {
  private fileService: FileService;
  private databaseService: DatabaseService;
  private initialized = false;

  constructor() {
    this.fileService = new FileService();
    this.databaseService = new DatabaseService();
  }

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      await this.fileService.initialize();
      await this.databaseService.initialize();
      this.initialized = true;
      console.log('Notes service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize notes service:', error);
      throw error;
    }
  }

  async loadAllNotes(): Promise<{
    rawNotes: RawMaterialNote[];
    summarizedNotes: SummarizedNote[];
  }> {
    if (!this.initialized) await this.initialize();

    try {
      // Load notes from file system
      const { rawNotes, summarizedNotes } = await this.fileService.loadAllNotes();

      // Sync with database
      for (const note of [...rawNotes, ...summarizedNotes]) {
        await this.databaseService.saveNoteMetadata(note);
      }

      return { rawNotes, summarizedNotes };
    } catch (error) {
      console.error('Failed to load all notes:', error);
      return { rawNotes: [], summarizedNotes: [] };
    }
  }

  async createNote(params: CreateNoteParams): Promise<string> {
    if (!this.initialized) await this.initialize();
    try {
      const id = crypto.randomUUID();
      const now = new Date();
      if (params.type === 'raw') {
        const newNote: RawMaterialNote = {
          id,
          title: params.title,
          content: params.content || '',
          sourceType: params.sourceType || 'other',
          sourceUrl: params.sourceUrl,
          tags: params.tags || [],
          createdAt: now,
          updatedAt: now,
          filePath: ''
        };
        const savedNote = await this.fileService.saveNote(newNote);
        await this.databaseService.saveNoteMetadata(savedNote);
        return id;
      } else {
        const newNote: SummarizedNote = {
          id,
          title: params.title,
          content: params.content || '',
          linkedRawNotes: params.linkedRawNotes || [],
          tags: params.tags || [],
          createdAt: now,
          updatedAt: now,
          filePath: ''
        };
        const savedNote = await this.fileService.saveNote(newNote);
        await this.databaseService.saveNoteMetadata(savedNote);
        return id;
      }
    } catch (error) {
      console.error('Failed to create note:', error);
      throw error;
    }
  }

  async updateNote(params: UpdateNoteParams): Promise<void> {
    if (!this.initialized) await this.initialize();

    try {
      // Get existing note metadata
      const metadata = await this.databaseService.getNoteMetadata(params.id);
      if (!metadata) {
        throw new Error(`Note with id ${params.id} not found`);
      }

      // Load the full note from file
      const existingNote = await this.fileService.loadNote(metadata.filePath);
      if (!existingNote) {
        throw new Error(`Note file not found: ${metadata.filePath}`);
      }

      // Update the note
      const updatedNote = {
        ...existingNote,
        ...params,
        updatedAt: new Date()
      };

      // Save to file and database
      await this.fileService.saveNote(updatedNote);
      await this.databaseService.saveNoteMetadata(updatedNote);
    } catch (error) {
      console.error('Failed to update note:', error);
      throw error;
    }
  }

  async deleteNote(id: string): Promise<void> {
    if (!this.initialized) await this.initialize();

    try {
      // Get note metadata to find file path
      const metadata = await this.databaseService.getNoteMetadata(id);
      if (!metadata) {
        console.warn(`Note with id ${id} not found in database`);
        return;
      }

      // Delete file
      await this.fileService.deleteNote(metadata.filePath);
      
      // Delete metadata
      await this.databaseService.deleteNoteMetadata(id);
    } catch (error) {
      console.error('Failed to delete note:', error);
      throw error;
    }
  }

  async getNoteById(id: string): Promise<RawMaterialNote | SummarizedNote | null> {
    if (!this.initialized) await this.initialize();

    try {
      const metadata = await this.databaseService.getNoteMetadata(id);
      if (!metadata) return null;

      return await this.fileService.loadNote(metadata.filePath);
    } catch (error) {
      console.error('Failed to get note by id:', error);
      return null;
    }
  }

  async searchNotes(query: string): Promise<(RawMaterialNote | SummarizedNote)[]> {
    if (!this.initialized) await this.initialize();

    try {
      const metadataResults = await this.databaseService.searchNotes(query);
      const notes: (RawMaterialNote | SummarizedNote)[] = [];

      for (const metadata of metadataResults) {
        const note = await this.fileService.loadNote(metadata.filePath);
        if (note) {
          notes.push(note);
        }
      }

      return notes;
    } catch (error) {
      console.error('Failed to search notes:', error);
      return [];
    }
  }

  async getNotesWithTag(tag: string): Promise<(RawMaterialNote | SummarizedNote)[]> {
    if (!this.initialized) await this.initialize();

    try {
      const metadataResults = await this.databaseService.getNotesWithTag(tag);
      const notes: (RawMaterialNote | SummarizedNote)[] = [];

      for (const metadata of metadataResults) {
        const note = await this.fileService.loadNote(metadata.filePath);
        if (note) {
          notes.push(note);
        }
      }

      return notes;
    } catch (error) {
      console.error('Failed to get notes with tag:', error);
      return [];
    }
  }

  async getAllTags(): Promise<string[]> {
    if (!this.initialized) await this.initialize();

    try {
      const tags = await this.databaseService.getAllTags();
      return tags.map(tag => tag.name);
    } catch (error) {
      console.error('Failed to get all tags:', error);
      return [];
    }
  }

  async exportNote(id: string, exportPath: string): Promise<void> {
    if (!this.initialized) await this.initialize();

    try {
      const note = await this.getNoteById(id);
      if (!note) {
        throw new Error(`Note with id ${id} not found`);
      }

      await this.fileService.exportNote(note, exportPath);
    } catch (error) {
      console.error('Failed to export note:', error);
      throw error;
    }
  }

  async importNote(filePath: string): Promise<string | null> {
    if (!this.initialized) await this.initialize();
    try {
      const note = await this.fileService.importNote(filePath);
      if (!note) {
        throw new Error('Failed to parse imported note');
      }
      const newId = crypto.randomUUID();
      const importedNote = {
        ...note,
        id: newId,
        updatedAt: new Date()
      };
      const savedNote = await this.fileService.saveNote(importedNote);
      await this.databaseService.saveNoteMetadata(savedNote);
      return newId;
    } catch (error) {
      console.error('Failed to import note:', error);
      throw error;
    }
  }

  async getNotesDirectory(): Promise<string> {
    if (!this.initialized) await this.initialize();
    return this.fileService.getNotesDirectory();
  }

  async setNotesDirectory(newPath: string): Promise<void> {
    if (!this.initialized) await this.initialize();
    await this.fileService.setNotesDirectory(newPath);
  }

  async close(): Promise<void> {
    await this.databaseService.close();
  }
}

// Singleton instance
export const notesService = new NotesService();
