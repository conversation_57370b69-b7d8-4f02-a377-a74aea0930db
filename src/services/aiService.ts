import type { AIResponse, AppSettings } from '../types';

export interface AIProvider {
  name: string;
  generateText(prompt: string, context?: string): Promise<AIResponse>;
  summarizeNotes(notes: string[]): Promise<AIResponse>;
  editText(text: string, instruction: string): Promise<AIResponse>;
  validateApiKey(): Promise<boolean>;
}

export interface AIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export class OpenAIProvider implements AIProvider {
  name = 'OpenAI';
  private apiKey: string;
  private baseUrl = 'https://api.openai.com/v1';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async generateText(prompt: string, context?: string): Promise<AIResponse> {
    try {
      const messages: AIMessage[] = [
        {
          role: 'system',
          content: 'You are a helpful AI assistant for note-taking and knowledge management. Provide clear, concise, and well-structured responses.'
        }
      ];

      if (context) {
        messages.push({
          role: 'system',
          content: `Context: ${context}`
        });
      }

      messages.push({
        role: 'user',
        content: prompt
      });

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-3.5-turbo',
          messages,
          max_tokens: 2000,
          temperature: 0.7,
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const content = data.choices?.[0]?.message?.content;

      if (!content) {
        throw new Error('No content received from OpenAI API');
      }

      return {
        success: true,
        content: content.trim()
      };
    } catch (error) {
      console.error('OpenAI API error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async summarizeNotes(notes: string[]): Promise<AIResponse> {
    const combinedNotes = notes.join('\n\n---\n\n');
    const prompt = `Please create a comprehensive summary of the following notes. Focus on key insights, main themes, and important connections between ideas:\n\n${combinedNotes}`;
    
    return this.generateText(prompt);
  }

  async editText(text: string, instruction: string): Promise<AIResponse> {
    const prompt = `Please edit the following text according to this instruction: "${instruction}"\n\nOriginal text:\n${text}\n\nProvide only the edited version:`;
    
    return this.generateText(prompt);
  }

  async validateApiKey(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
      });
      return response.ok;
    } catch {
      return false;
    }
  }
}

export class AnthropicProvider implements AIProvider {
  name = 'Anthropic';
  private apiKey: string;
  private baseUrl = 'https://api.anthropic.com/v1';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async generateText(prompt: string, context?: string): Promise<AIResponse> {
    try {
      let fullPrompt = prompt;
      if (context) {
        fullPrompt = `Context: ${context}\n\n${prompt}`;
      }

      const response = await fetch(`${this.baseUrl}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01',
        },
        body: JSON.stringify({
          model: 'claude-3-sonnet-20240229',
          max_tokens: 2000,
          messages: [
            {
              role: 'user',
              content: fullPrompt
            }
          ],
        }),
      });

      if (!response.ok) {
        throw new Error(`Anthropic API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const content = data.content?.[0]?.text;

      if (!content) {
        throw new Error('No content received from Anthropic API');
      }

      return {
        success: true,
        content: content.trim()
      };
    } catch (error) {
      console.error('Anthropic API error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async summarizeNotes(notes: string[]): Promise<AIResponse> {
    const combinedNotes = notes.join('\n\n---\n\n');
    const prompt = `Please create a comprehensive summary of the following notes. Focus on key insights, main themes, and important connections between ideas:\n\n${combinedNotes}`;
    
    return this.generateText(prompt);
  }

  async editText(text: string, instruction: string): Promise<AIResponse> {
    const prompt = `Please edit the following text according to this instruction: "${instruction}"\n\nOriginal text:\n${text}\n\nProvide only the edited version:`;
    
    return this.generateText(prompt);
  }

  async validateApiKey(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01',
        },
        body: JSON.stringify({
          model: 'claude-3-sonnet-20240229',
          max_tokens: 10,
          messages: [
            {
              role: 'user',
              content: 'Hello'
            }
          ],
        }),
      });
      return response.ok;
    } catch {
      return false;
    }
  }
}

export class OllamaProvider implements AIProvider {
  name = 'Ollama';
  private endpoint: string;
  private model: string;

  constructor(endpoint: string = 'http://localhost:11434', model: string = 'llama2') {
    this.endpoint = endpoint.replace(/\/$/, ''); // Remove trailing slash
    this.model = model;
  }

  async generateText(prompt: string, context?: string): Promise<AIResponse> {
    try {
      let fullPrompt = prompt;
      if (context) {
        fullPrompt = `Context: ${context}\n\n${prompt}`;
      }

      const response = await fetch(`${this.endpoint}/api/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: this.model,
          prompt: fullPrompt,
          stream: false,
        }),
      });

      if (!response.ok) {
        throw new Error(`Ollama API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const content = data.response;

      if (!content) {
        throw new Error('No content received from Ollama API');
      }

      return {
        success: true,
        content: content.trim()
      };
    } catch (error) {
      console.error('Ollama API error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async summarizeNotes(notes: string[]): Promise<AIResponse> {
    const combinedNotes = notes.join('\n\n---\n\n');
    const prompt = `Please create a comprehensive summary of the following notes. Focus on key insights, main themes, and important connections between ideas:\n\n${combinedNotes}`;
    
    return this.generateText(prompt);
  }

  async editText(text: string, instruction: string): Promise<AIResponse> {
    const prompt = `Please edit the following text according to this instruction: "${instruction}"\n\nOriginal text:\n${text}\n\nProvide only the edited version:`;
    
    return this.generateText(prompt);
  }

  async validateApiKey(): Promise<boolean> {
    try {
      const response = await fetch(`${this.endpoint}/api/tags`);
      return response.ok;
    } catch {
      return false;
    }
  }

  async setModel(model: string): Promise<boolean> {
    try {
      // Check if model is available
      const response = await fetch(`${this.endpoint}/api/tags`);
      if (!response.ok) return false;
      
      const data = await response.json();
      const availableModels = data.models?.map((m: any) => m.name) || [];
      
      if (availableModels.includes(model)) {
        this.model = model;
        return true;
      }
      return false;
    } catch {
      return false;
    }
  }

  async getAvailableModels(): Promise<string[]> {
    try {
      const response = await fetch(`${this.endpoint}/api/tags`);
      if (!response.ok) return [];

      const data = await response.json();
      return data.models?.map((m: any) => m.name) || [];
    } catch {
      return [];
    }
  }
}

export class AIService {
  private currentProvider: AIProvider | null = null;
  private settings: AppSettings | null = null;

  constructor() {
    // Initialize with settings from store if available
    this.loadSettings();
  }

  private loadSettings() {
    // This will be called from the settings store
    // For now, we'll set it up to be called externally
  }

  updateSettings(settings: AppSettings) {
    this.settings = settings;
    this.initializeProvider();
  }

  private initializeProvider() {
    if (!this.settings) return;

    try {
      switch (this.settings.aiProvider) {
        case 'openai':
          if (this.settings.apiKey) {
            this.currentProvider = new OpenAIProvider(this.settings.apiKey);
          }
          break;
        case 'anthropic':
          if (this.settings.apiKey) {
            this.currentProvider = new AnthropicProvider(this.settings.apiKey);
          }
          break;
        case 'ollama':
          this.currentProvider = new OllamaProvider(this.settings.ollamaEndpoint);
          break;
        default:
          this.currentProvider = null;
      }
    } catch (error) {
      console.error('Failed to initialize AI provider:', error);
      this.currentProvider = null;
    }
  }

  async generateText(prompt: string, context?: string): Promise<AIResponse> {
    if (!this.currentProvider) {
      return {
        success: false,
        error: 'No AI provider configured. Please check your settings.'
      };
    }

    return this.currentProvider.generateText(prompt, context);
  }

  async summarizeNotes(notes: string[]): Promise<AIResponse> {
    if (!this.currentProvider) {
      return {
        success: false,
        error: 'No AI provider configured. Please check your settings.'
      };
    }

    if (notes.length === 0) {
      return {
        success: false,
        error: 'No notes provided for summarization.'
      };
    }

    return this.currentProvider.summarizeNotes(notes);
  }

  async editText(text: string, instruction: string): Promise<AIResponse> {
    if (!this.currentProvider) {
      return {
        success: false,
        error: 'No AI provider configured. Please check your settings.'
      };
    }

    if (!text.trim()) {
      return {
        success: false,
        error: 'No text provided for editing.'
      };
    }

    return this.currentProvider.editText(text, instruction);
  }

  async validateCurrentProvider(): Promise<boolean> {
    if (!this.currentProvider) return false;
    return this.currentProvider.validateApiKey();
  }

  getCurrentProviderName(): string | null {
    return this.currentProvider?.name || null;
  }

  isConfigured(): boolean {
    return this.currentProvider !== null;
  }

  // Predefined AI commands for common tasks
  async rewriteText(text: string, style: 'formal' | 'casual' | 'academic' | 'creative'): Promise<AIResponse> {
    const styleInstructions = {
      formal: 'Rewrite this text in a formal, professional tone',
      casual: 'Rewrite this text in a casual, conversational tone',
      academic: 'Rewrite this text in an academic, scholarly tone with proper citations style',
      creative: 'Rewrite this text in a creative, engaging style'
    };

    return this.editText(text, styleInstructions[style]);
  }

  async expandText(text: string): Promise<AIResponse> {
    return this.editText(text, 'Expand this text with more details, examples, and explanations while maintaining the original meaning');
  }

  async condenseText(text: string): Promise<AIResponse> {
    return this.editText(text, 'Condense this text to be more concise while preserving all key information');
  }

  async fixGrammar(text: string): Promise<AIResponse> {
    return this.editText(text, 'Fix any grammar, spelling, and punctuation errors in this text');
  }

  async translateText(text: string, targetLanguage: string): Promise<AIResponse> {
    return this.editText(text, `Translate this text to ${targetLanguage}`);
  }

  async explainConcept(text: string): Promise<AIResponse> {
    return this.generateText(`Please explain the following concept in simple terms: ${text}`);
  }

  async generateOutline(topic: string): Promise<AIResponse> {
    return this.generateText(`Create a detailed outline for the topic: ${topic}`);
  }

  async findConnections(notes: string[]): Promise<AIResponse> {
    const combinedNotes = notes.join('\n\n---\n\n');
    return this.generateText(`Analyze the following notes and identify key connections, patterns, and relationships between ideas:\n\n${combinedNotes}`);
  }
}

// Singleton instance
export const aiService = new AIService();
