import type { RawMaterialNote, SummarizedNote, NotionPage } from '../types';

export interface NotionConfig {
  apiKey: string;
  databaseId?: string;
}

export class NotionService {
  private apiKey: string;
  private baseUrl = 'https://api.notion.com/v1';
  private version = '2022-06-28';

  constructor(config: NotionConfig) {
    this.apiKey = config.apiKey;
  }

  private async makeRequest(endpoint: string, options: RequestInit = {}) {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Notion-Version': this.version,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      throw new Error(`Notion API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async validateConnection(): Promise<boolean> {
    try {
      await this.makeRequest('/users/me');
      return true;
    } catch {
      return false;
    }
  }

  async getDatabases(): Promise<any[]> {
    try {
      const response = await this.makeRequest('/search', {
        method: 'POST',
        body: JSON.stringify({
          filter: {
            value: 'database',
            property: 'object'
          }
        })
      });
      return response.results || [];
    } catch (error) {
      console.error('Failed to get databases:', error);
      return [];
    }
  }

  async getPages(databaseId: string): Promise<NotionPage[]> {
    try {
      const response = await this.makeRequest(`/databases/${databaseId}/query`, {
        method: 'POST',
        body: JSON.stringify({})
      });

      return response.results.map((page: any) => ({
        id: page.id,
        title: this.extractTitle(page),
        content: page, // Store full page object for now
        properties: page.properties,
        createdTime: page.created_time,
        lastEditedTime: page.last_edited_time
      }));
    } catch (error) {
      console.error('Failed to get pages:', error);
      return [];
    }
  }

  private extractTitle(page: any): string {
    // Try to find title property
    for (const [, value] of Object.entries(page.properties)) {
      if ((value as any).type === 'title' && (value as any).title?.length > 0) {
        return (value as any).title[0].plain_text;
      }
    }
    return 'Untitled';
  }

  async getPageContent(pageId: string): Promise<string> {
    try {
      const response = await this.makeRequest(`/blocks/${pageId}/children`);
      return this.blocksToMarkdown(response.results);
    } catch (error) {
      console.error('Failed to get page content:', error);
      return '';
    }
  }

  private blocksToMarkdown(blocks: any[]): string {
    return blocks.map(block => this.blockToMarkdown(block)).join('\n\n');
  }

  private blockToMarkdown(block: any): string {
    switch (block.type) {
      case 'paragraph':
        return this.richTextToMarkdown(block.paragraph.rich_text);
      
      case 'heading_1':
        return `# ${this.richTextToMarkdown(block.heading_1.rich_text)}`;
      
      case 'heading_2':
        return `## ${this.richTextToMarkdown(block.heading_2.rich_text)}`;
      
      case 'heading_3':
        return `### ${this.richTextToMarkdown(block.heading_3.rich_text)}`;
      
      case 'bulleted_list_item':
        return `- ${this.richTextToMarkdown(block.bulleted_list_item.rich_text)}`;
      
      case 'numbered_list_item':
        return `1. ${this.richTextToMarkdown(block.numbered_list_item.rich_text)}`;
      
      case 'code':
        const language = block.code.language || '';
        const code = this.richTextToMarkdown(block.code.rich_text);
        return `\`\`\`${language}\n${code}\n\`\`\``;
      
      case 'quote':
        return `> ${this.richTextToMarkdown(block.quote.rich_text)}`;
      
      case 'divider':
        return '---';
      
      default:
        return '';
    }
  }

  private richTextToMarkdown(richText: any[]): string {
    return richText.map(text => {
      let content = text.plain_text;
      
      if (text.annotations.bold) content = `**${content}**`;
      if (text.annotations.italic) content = `*${content}*`;
      if (text.annotations.code) content = `\`${content}\``;
      if (text.annotations.strikethrough) content = `~~${content}~~`;
      if (text.href) content = `[${content}](${text.href})`;
      
      return content;
    }).join('');
  }

  async importPageAsNote(pageId: string): Promise<RawMaterialNote | null> {
    try {
      const page = await this.makeRequest(`/pages/${pageId}`);
      const content = await this.getPageContent(pageId);
      const title = this.extractTitle(page);

      const note: RawMaterialNote = {
        id: crypto.randomUUID(),
        title,
        content,
        sourceType: 'other',
        sourceUrl: `https://notion.so/${pageId.replace(/-/g, '')}`,
        tags: [],
        createdAt: new Date(page.created_time),
        updatedAt: new Date(page.last_edited_time),
        filePath: ''
      };

      return note;
    } catch (error) {
      console.error('Failed to import page:', error);
      return null;
    }
  }

  async exportNoteToNotion(note: RawMaterialNote | SummarizedNote, databaseId: string): Promise<string | null> {
    try {
      const blocks = this.markdownToBlocks(note.content);
      
      const pageData = {
        parent: {
          database_id: databaseId
        },
        properties: {
          Name: {
            title: [
              {
                text: {
                  content: note.title
                }
              }
            ]
          },
          Tags: {
            multi_select: note.tags.map(tag => ({ name: tag }))
          }
        },
        children: blocks
      };

      const response = await this.makeRequest('/pages', {
        method: 'POST',
        body: JSON.stringify(pageData)
      });

      return response.id;
    } catch (error) {
      console.error('Failed to export note:', error);
      return null;
    }
  }

  private markdownToBlocks(markdown: string): any[] {
    const lines = markdown.split('\n');
    const blocks: any[] = [];

    for (const line of lines) {
      if (line.trim() === '') continue;

      if (line.startsWith('# ')) {
        blocks.push({
          object: 'block',
          type: 'heading_1',
          heading_1: {
            rich_text: [{ type: 'text', text: { content: line.substring(2) } }]
          }
        });
      } else if (line.startsWith('## ')) {
        blocks.push({
          object: 'block',
          type: 'heading_2',
          heading_2: {
            rich_text: [{ type: 'text', text: { content: line.substring(3) } }]
          }
        });
      } else if (line.startsWith('### ')) {
        blocks.push({
          object: 'block',
          type: 'heading_3',
          heading_3: {
            rich_text: [{ type: 'text', text: { content: line.substring(4) } }]
          }
        });
      } else if (line.startsWith('- ') || line.startsWith('* ')) {
        blocks.push({
          object: 'block',
          type: 'bulleted_list_item',
          bulleted_list_item: {
            rich_text: [{ type: 'text', text: { content: line.substring(2) } }]
          }
        });
      } else if (line.match(/^\d+\. /)) {
        blocks.push({
          object: 'block',
          type: 'numbered_list_item',
          numbered_list_item: {
            rich_text: [{ type: 'text', text: { content: line.replace(/^\d+\. /, '') } }]
          }
        });
      } else if (line.startsWith('> ')) {
        blocks.push({
          object: 'block',
          type: 'quote',
          quote: {
            rich_text: [{ type: 'text', text: { content: line.substring(2) } }]
          }
        });
      } else if (line === '---') {
        blocks.push({
          object: 'block',
          type: 'divider',
          divider: {}
        });
      } else {
        blocks.push({
          object: 'block',
          type: 'paragraph',
          paragraph: {
            rich_text: [{ type: 'text', text: { content: line } }]
          }
        });
      }
    }

    return blocks;
  }

  async syncNotes(databaseId: string, localNotes: (RawMaterialNote | SummarizedNote)[]): Promise<{
    imported: number;
    exported: number;
    errors: string[];
  }> {
    const result: { imported: number; exported: number; errors: string[] } = { imported: 0, exported: 0, errors: [] };

    try {
      // Get all pages from Notion database
      const notionPages = await this.getPages(databaseId);
      
      // Import new pages from Notion
      for (const page of notionPages) {
        try {
          const note = await this.importPageAsNote(page.id);
          if (note) {
            // Check if note already exists locally
            const exists = localNotes.some(localNote => 
              'sourceUrl' in localNote && localNote.sourceUrl === note.sourceUrl
            );
            
            if (!exists) {
              // TODO: Add to local notes store
              result.imported++;
            }
          }
        } catch (error) {
          result.errors.push(`Failed to import page ${page.title}: ${error}`);
        }
      }

      // Export local notes that don't exist in Notion
      for (const note of localNotes) {
        try {
          if ('sourceUrl' in note && note.sourceUrl?.includes('notion.so')) {
            const pageId = await this.exportNoteToNotion(note, databaseId);
            if (pageId) {
              result.exported++;
            }
          }
        } catch (error) {
          result.errors.push(`Failed to export note ${note.title}: ${error}`);
        }
      }

    } catch (error) {
      result.errors.push(`Sync failed: ${error}`);
    }

    return result;
  }
}
