import Database from '@tauri-apps/plugin-sql';
import type { RawMaterialNote, SummarizedNote } from '../types';

export interface NoteMetadata {
  id: string;
  title: string;
  type: 'raw' | 'summary';
  tags: string;
  filePath: string;
  createdAt: string;
  updatedAt: string;
  sourceType?: string;
  sourceUrl?: string;
  linkedRawNotes?: string;
}

export class DatabaseService {
  private db: Database | null = null;

  async initialize(): Promise<void> {
    try {
      this.db = await Database.load('sqlite:synthesis.db');
      await this.createTables();
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      // Create notes metadata table
      await this.db.execute(`
        CREATE TABLE IF NOT EXISTS notes (
          id TEXT PRIMARY KEY,
          title TEXT NOT NULL,
          type TEXT NOT NULL CHECK (type IN ('raw', 'summary')),
          tags TEXT DEFAULT '',
          file_path TEXT NOT NULL,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          source_type TEXT,
          source_url TEXT,
          linked_raw_notes TEXT DEFAULT ''
        )
      `);

      // Create tags table for better tag management
      await this.db.execute(`
        CREATE TABLE IF NOT EXISTS tags (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT UNIQUE NOT NULL,
          usage_count INTEGER DEFAULT 0
        )
      `);

      // Create note_tags junction table
      await this.db.execute(`
        CREATE TABLE IF NOT EXISTS note_tags (
          note_id TEXT,
          tag_name TEXT,
          PRIMARY KEY (note_id, tag_name),
          FOREIGN KEY (note_id) REFERENCES notes (id) ON DELETE CASCADE,
          FOREIGN KEY (tag_name) REFERENCES tags (name) ON DELETE CASCADE
        )
      `);

      // Create full-text search virtual table
      await this.db.execute(`
        CREATE VIRTUAL TABLE IF NOT EXISTS notes_fts USING fts5(
          id,
          title,
          content,
          tags,
          content='notes',
          content_rowid='rowid'
        )
      `);

      console.log('Database tables created successfully');
    } catch (error) {
      console.error('Failed to create database tables:', error);
      throw error;
    }
  }

  async saveNoteMetadata(note: RawMaterialNote | SummarizedNote): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      const isRawNote = 'sourceType' in note;
      const metadata: NoteMetadata = {
        id: note.id,
        title: note.title,
        type: isRawNote ? 'raw' : 'summary',
        tags: note.tags.join(','),
        filePath: note.filePath,
        createdAt: note.createdAt.toISOString(),
        updatedAt: note.updatedAt.toISOString(),
      };

      if (isRawNote) {
        const rawNote = note as RawMaterialNote;
        metadata.sourceType = rawNote.sourceType;
        metadata.sourceUrl = rawNote.sourceUrl;
      } else {
        const summaryNote = note as SummarizedNote;
        metadata.linkedRawNotes = summaryNote.linkedRawNotes.join(',');
      }

      // Insert or update note metadata
      await this.db.execute(`
        INSERT OR REPLACE INTO notes (
          id, title, type, tags, file_path, created_at, updated_at,
          source_type, source_url, linked_raw_notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        metadata.id,
        metadata.title,
        metadata.type,
        metadata.tags,
        metadata.filePath,
        metadata.createdAt,
        metadata.updatedAt,
        metadata.sourceType || null,
        metadata.sourceUrl || null,
        metadata.linkedRawNotes || ''
      ]);

      // Update tags
      await this.updateTags(note.id, note.tags);

      // Update FTS index
      await this.updateFTSIndex(note);
    } catch (error) {
      console.error('Failed to save note metadata:', error);
      throw error;
    }
  }

  private async updateTags(noteId: string, tags: string[]): Promise<void> {
    if (!this.db) return;

    try {
      // Remove existing tag associations
      await this.db.execute('DELETE FROM note_tags WHERE note_id = ?', [noteId]);

      // Add new tag associations
      for (const tag of tags) {
        if (tag.trim()) {
          // Insert or update tag
          await this.db.execute(`
            INSERT OR IGNORE INTO tags (name, usage_count) VALUES (?, 0)
          `, [tag]);

          await this.db.execute(`
            UPDATE tags SET usage_count = usage_count + 1 WHERE name = ?
          `, [tag]);

          // Create association
          await this.db.execute(`
            INSERT INTO note_tags (note_id, tag_name) VALUES (?, ?)
          `, [noteId, tag]);
        }
      }
    } catch (error) {
      console.error('Failed to update tags:', error);
    }
  }

  private async updateFTSIndex(note: RawMaterialNote | SummarizedNote): Promise<void> {
    if (!this.db) return;

    try {
      await this.db.execute(`
        INSERT OR REPLACE INTO notes_fts (id, title, content, tags)
        VALUES (?, ?, ?, ?)
      `, [
        note.id,
        note.title,
        note.content,
        note.tags.join(' ')
      ]);
    } catch (error) {
      console.error('Failed to update FTS index:', error);
    }
  }

  async getNoteMetadata(id: string): Promise<NoteMetadata | null> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      const result = await this.db.select<NoteMetadata[]>(`
        SELECT * FROM notes WHERE id = ?
      `, [id]);

      return result.length > 0 ? result[0] : null;
    } catch (error) {
      console.error('Failed to get note metadata:', error);
      return null;
    }
  }

  async getAllNotesMetadata(): Promise<NoteMetadata[]> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      return await this.db.select<NoteMetadata[]>(`
        SELECT * FROM notes ORDER BY updated_at DESC
      `);
    } catch (error) {
      console.error('Failed to get all notes metadata:', error);
      return [];
    }
  }

  async searchNotes(query: string): Promise<NoteMetadata[]> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      // Use FTS for full-text search
      const results = await this.db.select<NoteMetadata[]>(`
        SELECT n.* FROM notes n
        JOIN notes_fts fts ON n.id = fts.id
        WHERE notes_fts MATCH ?
        ORDER BY rank
      `, [query]);

      return results;
    } catch (error) {
      console.error('Failed to search notes:', error);
      // Fallback to simple LIKE search
      return await this.db.select<NoteMetadata[]>(`
        SELECT * FROM notes 
        WHERE title LIKE ? OR tags LIKE ?
        ORDER BY updated_at DESC
      `, [`%${query}%`, `%${query}%`]);
    }
  }

  async getNotesWithTag(tag: string): Promise<NoteMetadata[]> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      return await this.db.select<NoteMetadata[]>(`
        SELECT n.* FROM notes n
        JOIN note_tags nt ON n.id = nt.note_id
        WHERE nt.tag_name = ?
        ORDER BY n.updated_at DESC
      `, [tag]);
    } catch (error) {
      console.error('Failed to get notes with tag:', error);
      return [];
    }
  }

  async getAllTags(): Promise<{ name: string; count: number }[]> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      return await this.db.select<{ name: string; count: number }[]>(`
        SELECT name, usage_count as count FROM tags 
        WHERE usage_count > 0 
        ORDER BY usage_count DESC, name ASC
      `);
    } catch (error) {
      console.error('Failed to get all tags:', error);
      return [];
    }
  }

  async deleteNoteMetadata(id: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      // Get tags before deletion to update counts
      const note = await this.getNoteMetadata(id);
      if (note && note.tags) {
        const tags = note.tags.split(',').filter(tag => tag.trim());
        for (const tag of tags) {
          await this.db.execute(`
            UPDATE tags SET usage_count = usage_count - 1 WHERE name = ?
          `, [tag]);
        }
      }

      // Delete note metadata (cascades to note_tags)
      await this.db.execute('DELETE FROM notes WHERE id = ?', [id]);
      
      // Delete from FTS index
      await this.db.execute('DELETE FROM notes_fts WHERE id = ?', [id]);

      // Clean up unused tags
      await this.db.execute('DELETE FROM tags WHERE usage_count <= 0');
    } catch (error) {
      console.error('Failed to delete note metadata:', error);
      throw error;
    }
  }

  async close(): Promise<void> {
    if (this.db) {
      await this.db.close();
      this.db = null;
    }
  }
}
