import { listen } from '@tauri-apps/api/event';

export class FileWatcherService {
  private unlisten: (() => void) | null = null;

  async watchNotesDirectory(onChange: () => void) {
    this.unlisten = await listen('tauri://file-drop', (_event) => {
      // This event is a placeholder; use the correct Tauri FS event for your setup
      onChange();
    });
    // TODO: Use <PERSON>ri's file system watcher plugin for more robust watching
  }

  async unwatch() {
    if (this.unlisten) {
      this.unlisten();
      this.unlisten = null;
    }
  }
}

export const fileWatcherService = new FileWatcherService(); 