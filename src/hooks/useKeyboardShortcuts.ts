import { useEffect } from 'react';
import { useUIStore } from '../stores/uiStore';
import { useNotesStore } from '../stores/notesStore';

export interface KeyboardShortcut {
  key: string;
  metaKey?: boolean;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  action: () => void;
  description: string;
}

export function useKeyboardShortcuts() {
  const { 
    toggleSidebar, 
    setCurrentView, 
    toggleCommandPalette,
    commandPaletteOpen 
  } = useUIStore();
  
  const { createNote } = useNotesStore();

  const shortcuts: KeyboardShortcut[] = [
    {
      key: 'b',
      metaKey: true,
      action: toggleSidebar,
      description: 'Toggle sidebar'
    },
    {
      key: 'n',
      metaKey: true,
      action: () => createNote({ title: 'New Note', type: 'raw' }),
      description: 'Create new note'
    },
    {
      key: 'k',
      metaKey: true,
      action: () => toggleCommandPalette(),
      description: 'Open command palette'
    },
    {
      key: '1',
      metaKey: true,
      action: () => setCurrentView('editor'),
      description: 'Switch to editor view'
    },
    {
      key: '2',
      metaKey: true,
      action: () => setCurrentView('split'),
      description: 'Switch to split view'
    },
    {
      key: '3',
      metaKey: true,
      action: () => setCurrentView('preview'),
      description: 'Switch to preview view'
    },
    {
      key: 'Escape',
      action: () => {
        if (commandPaletteOpen) {
          toggleCommandPalette();
        }
      },
      description: 'Close dialogs/panels'
    }
  ];

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in inputs
      if (
        event.target instanceof HTMLInputElement ||
        event.target instanceof HTMLTextAreaElement ||
        event.target instanceof HTMLSelectElement ||
        (event.target as HTMLElement)?.contentEditable === 'true'
      ) {
        // Allow Escape to work in inputs
        if (event.key !== 'Escape') {
          return;
        }
      }

      for (const shortcut of shortcuts) {
        const metaMatch = shortcut.metaKey ? event.metaKey || event.ctrlKey : !event.metaKey && !event.ctrlKey;
        const ctrlMatch = shortcut.ctrlKey ? event.ctrlKey : !shortcut.ctrlKey || !event.ctrlKey;
        const shiftMatch = shortcut.shiftKey ? event.shiftKey : !event.shiftKey;
        const altMatch = shortcut.altKey ? event.altKey : !event.altKey;

        if (
          event.key.toLowerCase() === shortcut.key.toLowerCase() &&
          metaMatch &&
          ctrlMatch &&
          shiftMatch &&
          altMatch
        ) {
          event.preventDefault();
          shortcut.action();
          break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [shortcuts]);

  return { shortcuts };
}

// Hook for displaying keyboard shortcuts help
export function useKeyboardShortcutsHelp() {
  const { shortcuts } = useKeyboardShortcuts();

  const formatShortcut = (shortcut: KeyboardShortcut): string => {
    const parts: string[] = [];
    
    if (shortcut.metaKey) {
      parts.push(navigator.platform.includes('Mac') ? '⌘' : 'Ctrl');
    }
    if (shortcut.ctrlKey && !shortcut.metaKey) {
      parts.push('Ctrl');
    }
    if (shortcut.shiftKey) {
      parts.push('Shift');
    }
    if (shortcut.altKey) {
      parts.push(navigator.platform.includes('Mac') ? '⌥' : 'Alt');
    }
    
    parts.push(shortcut.key.toUpperCase());
    
    return parts.join(' + ');
  };

  const shortcutGroups = {
    'Navigation': shortcuts.filter(s => 
      ['b', '1', '2', '3', 'Escape'].includes(s.key)
    ),
    'Actions': shortcuts.filter(s => 
      ['n', 'k'].includes(s.key)
    )
  };

  return { shortcuts, formatShortcut, shortcutGroups };
}
