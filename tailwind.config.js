/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'Fira Code', 'monospace'],
      },
      colors: {
        border: 'rgb(229 231 235 / <alpha-value>)', // gray-200
        input: 'rgb(229 231 235 / <alpha-value>)', // gray-200
        ring: 'rgb(59 130 246 / <alpha-value>)', // blue-500
        background: 'rgb(255 255 255 / <alpha-value>)', // white
        foreground: 'rgb(17 24 39 / <alpha-value>)', // gray-900
        primary: {
          DEFAULT: 'rgb(59 130 246 / <alpha-value>)', // blue-500
          foreground: 'rgb(255 255 255 / <alpha-value>)', // white
        },
        secondary: {
          DEFAULT: 'rgb(243 244 246 / <alpha-value>)', // gray-100
          foreground: 'rgb(17 24 39 / <alpha-value>)', // gray-900
        },
        destructive: {
          DEFAULT: 'rgb(239 68 68 / <alpha-value>)', // red-500
          foreground: 'rgb(255 255 255 / <alpha-value>)', // white
        },
        muted: {
          DEFAULT: 'rgb(243 244 246 / <alpha-value>)', // gray-100
          foreground: 'rgb(107 114 128 / <alpha-value>)', // gray-500
        },
        accent: {
          DEFAULT: 'rgb(243 244 246 / <alpha-value>)', // gray-100
          foreground: 'rgb(17 24 39 / <alpha-value>)', // gray-900
        },
        popover: {
          DEFAULT: 'rgb(255 255 255 / <alpha-value>)', // white
          foreground: 'rgb(17 24 39 / <alpha-value>)', // gray-900
        },
        card: {
          DEFAULT: 'rgb(255 255 255 / <alpha-value>)', // white
          foreground: 'rgb(17 24 39 / <alpha-value>)', // gray-900
        },
      },
    },
  },
  plugins: [require('@tailwindcss/typography')],
}
