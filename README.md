# Synthesis

> **AI-powered note-taking desktop app for knowledge workers**

[![Build Status](#)](#) [![License](#)](#)

---

## Introduction

**Synthesis** is a modern, cross-platform note-taking application that leverages AI to help you capture, organize, and synthesize knowledge from books, articles, videos, and more. Built with React, Vite, Tailwind CSS, Zustand, and Tau<PERSON>, Synthesis runs as a fast, secure desktop app with local-first storage and powerful AI features.

---

## Features

- ✍️ **Create raw material notes** from any source (books, articles, videos, etc.)
- 🤖 **AI-powered editing**: Summarize, expand, condense, fix grammar, translate, and more (Cmd/Ctrl+K)
- 📝 **Visual diff**: Review AI-generated changes before applying
- 🏷️ **Tagging & search**: Organize and quickly find your notes
- 📤 **Export**: Send notes to Notion, Obsidian, or Markdown
- 📥 **Import**: Bring in notes from Notion, Obsidian, or Markdown
- 🎨 **Themes**: Light, dark, or system appearance
- 🔒 **Local-first**: Notes stored on your device, with SQLite metadata and markdown files
- ⚡ **Fast & offline**: Built with <PERSON><PERSON> for native performance

---

## Screenshots

> _Add screenshots here_

---

## Getting Started

### Prerequisites
- [Node.js](https://nodejs.org/) (v18+ recommended)
- [Rust & Cargo](https://www.rust-lang.org/tools/install) (for Tauri backend)
- [pnpm](https://pnpm.io/) or [npm](https://www.npmjs.com/) or [yarn](https://yarnpkg.com/)

### Installation

```bash
# Clone the repo
$ git clone <your-repo-url>
$ cd VSvode2

# Install dependencies
$ npm install

# (Optional) Install Tauri CLI globally
$ cargo install tauri-cli
```

### Development

```bash
# Start the frontend (Vite)
$ npm run dev

# In another terminal, start the Tauri app (desktop window)
$ npm run tauri dev
$ cargo tauri dev
```

### Build

```bash
# Build frontend and Tauri app for production
$ npm run build
$ cargo tauri build
```

### Testing

```bash
# Run unit and component tests
$ npm run test
```

---

## Configuration

Open the **Settings** dialog in the app to configure:

- **AI Provider**: Choose between OpenAI (GPT), Anthropic (Claude), or Ollama (local LLM)
  - For OpenAI/Anthropic: Enter your API key
  - For Ollama: Set the endpoint (default: http://localhost:11434)
- **Notes Directory**: Set a custom location for your notes (default: App Data/Synthesis/notes)
- **Theme**: Light, dark, or system

---

## Usage

- **Create a note**: Click "New Note" in the sidebar
- **Edit with AI**: Select text and press Cmd/Ctrl+K to open the AI command palette
- **Search**: Use the search bar to find notes by content or tag
- **Tagging**: Add tags to notes for better organization
- **Export/Import**: Use the Import/Export dialog to move notes to/from Notion, Obsidian, or Markdown

---

## Import & Export

- **Notion**: Requires Notion API key and database ID
- **Obsidian**: Export as vault-compatible markdown files
- **Markdown**: Standard markdown with frontmatter

---

## Keyboard Shortcuts

> _See the in-app Help dialog for a full list of shortcuts_

---

## Technologies Used

- [React](https://react.dev/) + [Vite](https://vitejs.dev/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Zustand](https://zustand-demo.pmnd.rs/)
- [Tauri](https://tauri.app/) (Rust backend)
- [SQLite](https://www.sqlite.org/index.html) (via Tauri plugin)
- [OpenAI](https://platform.openai.com/), [Anthropic](https://console.anthropic.com/), [Ollama](https://ollama.com/) (AI providers)

---

## Project Structure

```
VSvode2/
├── src/                # Frontend source (React, Zustand, etc.)
│   ├── components/     # UI components
│   ├── services/       # AI, file, database, notes, import/export
│   ├── stores/         # Zustand stores
│   ├── hooks/          # Custom hooks
│   ├── types/          # TypeScript types
│   └── ...
├── src-tauri/          # Tauri backend (Rust)
│   ├── src/            # Rust source
│   ├── Cargo.toml      # Rust dependencies
│   └── tauri.conf.json # Tauri config
├── package.json        # JS dependencies & scripts
├── vite.config.ts      # Vite config
└── ...
```

---

## License

> _Specify your license here_ 