import { describe, it, expect, beforeEach, vi } from 'vitest';
import { FileService } from '../src/services/fileService';

vi.mock('@tauri-apps/api/path', () => ({
  appDataDir: () => Promise.resolve('/mock/app/data'),
  join: (...paths: string[]) => Promise.resolve(paths.join('/')),
}));
vi.mock('@tauri-apps/plugin-fs', () => ({
  readTextFile: vi.fn(),
  writeTextFile: vi.fn(),
  exists: vi.fn(),
  create: vi.fn(),
  readDir: vi.fn(),
}));

const mockNote = {
  id: '1',
  title: 'Test Note',
  content: 'Hello world',
  sourceType: 'other',
  tags: [],
  createdAt: new Date(),
  updatedAt: new Date(),
  filePath: '/mock/app/data/synthesis/notes/raw/1.md',
};

describe('FileService', () => {
  let fileService: FileService;

  beforeEach(() => {
    fileService = new FileService();
  });

  it('initializes and creates directories', async () => {
    await expect(fileService.initialize()).resolves.not.toThrow();
  });

  it('loads notes and returns empty if none exist', async () => {
    const notes = await fileService.loadAllNotes();
    expect(notes.rawNotes).toBeDefined();
    expect(notes.summarizedNotes).toBeDefined();
  });

  // Add more CRUD and save/load tests as needed
}); 