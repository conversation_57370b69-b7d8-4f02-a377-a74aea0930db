import { describe, it, expect } from 'vitest';
import { ObsidianService } from '../src/services/obsidianService';

const mockRawNote = {
  id: '1',
  title: 'Test Note',
  content: 'Hello world',
  sourceType: 'other',
  tags: ['test'],
  createdAt: new Date(),
  updatedAt: new Date(),
  filePath: '/mock/path/1.md',
};

describe('ObsidianService', () => {
  let obsidianService: ObsidianService;

  beforeEach(() => {
    obsidianService = new ObsidianService();
  });

  it('should convert note to Obsidian format', () => {
    const md = obsidianService.noteToObsidianFormat(mockRawNote);
    expect(md).toContain('---');
    expect(md).toContain('Test Note');
  });

  it('should convert Obsidian format to note', () => {
    const md = obsidianService.noteToObsidianFormat(mockRawNote);
    const note = obsidianService.obsidianFormatToNote(md, '/mock/path/1.md');
    expect(note).toBeDefined();
    expect(note?.title).toBe('Test Note');
  });
}); 