import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AIService } from '../src/services/aiService';

global.fetch = vi.fn();

describe('AIService', () => {
  let aiService: AIService;

  beforeEach(() => {
    aiService = new AIService();
  });

  it('should not be configured by default', () => {
    expect(aiService.isConfigured()).toBe(false);
  });

  it('should update settings and initialize provider', () => {
    aiService.updateSettings({ aiProvider: 'openai', apiKey: 'sk-test', notesDirectory: '', theme: 'light' });
    expect(aiService.isConfigured()).toBe(true);
  });

  // Add more tests for text generation, summarization, and error handling
}); 