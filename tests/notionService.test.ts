import { describe, it, expect, beforeEach, vi } from 'vitest';
import { NotionService } from '../src/services/notionService';

global.fetch = vi.fn();

describe('NotionService', () => {
  let notionService: NotionService;

  beforeEach(() => {
    notionService = new NotionService({ apiKey: 'test-key' });
  });

  it('should initialize with API key', () => {
    expect(notionService).toBeDefined();
  });

  it('should convert markdown to blocks', () => {
    const blocks = notionService['markdownToBlocks']('# Heading\nParagraph');
    expect(blocks.length).toBeGreaterThan(0);
  });

  // Add more tests for import/export and error handling
}); 