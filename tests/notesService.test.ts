import { describe, it, expect, beforeEach, vi } from 'vitest';
import { NotesService } from '../src/services/notesService';

vi.mock('../src/services/fileService', () => ({
  FileService: vi.fn().mockImplementation(() => ({
    initialize: vi.fn(),
    loadAllNotes: vi.fn().mockResolvedValue({ rawNotes: [], summarizedNotes: [] }),
    saveNote: vi.fn(),
    deleteNote: vi.fn(),
    importNote: vi.fn(),
    getNotesDirectory: vi.fn(),
    setNotesDirectory: vi.fn(),
  })),
}));
vi.mock('../src/services/databaseService', () => ({
  DatabaseService: vi.fn().mockImplementation(() => ({
    initialize: vi.fn(),
    saveNoteMetadata: vi.fn(),
    getNoteMetadata: vi.fn(),
    deleteNoteMetadata: vi.fn(),
  })),
}));

describe('NotesService', () => {
  let notesService: NotesService;

  beforeEach(() => {
    notesService = new NotesService();
  });

  it('initializes and loads notes', async () => {
    await expect(notesService.initialize()).resolves.not.toThrow();
    const notes = await notesService.loadAllNotes();
    expect(notes.rawNotes).toBeDefined();
    expect(notes.summarizedNotes).toBeDefined();
  });

  // Add more CRUD and import/export tests as needed
}); 